"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"
import { useTasks } from "../contexts/TaskContext"

export default function TasksScreen({ navigation, route }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const { tasks, availableTasks, myTasks } = useTasks()
  const [activeTab, setActiveTab] = useState<"available" | "my" | "completed">("available")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState(route?.params?.category || "")

  const categories = [
    "Todas",
    "Limpieza",
    "Plomería",
    "Electricidad",
    "Jardinería",
    "Pintura",
    "Mudanza",
    "Carpintería",
    "Tecnología",
  ]

  const getFilteredTasks = () => {
    let filteredTasks = []

    switch (activeTab) {
      case "available":
        filteredTasks = user?.role === "worker" ? availableTasks : []
        break
      case "my":
        filteredTasks = myTasks
        break
      case "completed":
        filteredTasks = tasks.filter((task) => task.status === "completed")
        break
      default:
        filteredTasks = availableTasks
    }

    // Filter by category
    if (selectedCategory && selectedCategory !== "Todas") {
      filteredTasks = filteredTasks.filter((task) => task.category === selectedCategory)
    }

    // Filter by search query
    if (searchQuery) {
      filteredTasks = filteredTasks.filter(
        (task) =>
          task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          task.category.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    }

    return filteredTasks
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "#FF3B30"
      case "medium":
        return "#FF9500"
      case "low":
        return "#34C759"
      default:
        return colors.textSecondary
    }
  }

  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case "high":
        return "Urgente"
      case "medium":
        return "Moderado"
      case "low":
        return "Flexible"
      default:
        return ""
    }
  }

  const filteredTasks = getFilteredTasks()

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={[styles.title, { color: colors.text }]}>
          {user?.role === "client" ? "Mis Tareas" : "Trabajos Disponibles"}
        </Text>

        {/* Search Bar */}
        <View style={[styles.searchContainer, { backgroundColor: colors.background, borderColor: colors.border }]}>
          <Text style={styles.searchIcon}>🔍</Text>
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Buscar tareas..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Tabs */}
      <View style={[styles.tabsContainer, { backgroundColor: colors.surface }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabs}>
          {user?.role === "worker" && (
            <TouchableOpacity
              style={[
                styles.tab,
                {
                  backgroundColor: activeTab === "available" ? colors.primary : "transparent",
                },
              ]}
              onPress={() => setActiveTab("available")}
            >
              <Text style={[styles.tabText, { color: activeTab === "available" ? "#FFFFFF" : colors.text }]}>
                Disponibles ({availableTasks.length})
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.tab,
              {
                backgroundColor: activeTab === "my" ? colors.primary : "transparent",
              },
            ]}
            onPress={() => setActiveTab("my")}
          >
            <Text style={[styles.tabText, { color: activeTab === "my" ? "#FFFFFF" : colors.text }]}>
              Mis Tareas ({myTasks.length})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tab,
              {
                backgroundColor: activeTab === "completed" ? colors.primary : "transparent",
              },
            ]}
            onPress={() => setActiveTab("completed")}
          >
            <Text style={[styles.tabText, { color: activeTab === "completed" ? "#FFFFFF" : colors.text }]}>
              Completadas
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Categories Filter */}
      <View style={styles.categoriesContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.categories}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryChip,
                {
                  backgroundColor: selectedCategory === category ? colors.primary : colors.surface,
                  borderColor: colors.border,
                },
              ]}
              onPress={() => setSelectedCategory(category === "Todas" ? "" : category)}
            >
              <Text
                style={[styles.categoryChipText, { color: selectedCategory === category ? "#FFFFFF" : colors.text }]}
              >
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tasks List */}
      <ScrollView style={styles.tasksList} contentContainerStyle={styles.tasksContent}>
        {filteredTasks.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyEmoji}>📋</Text>
            <Text style={[styles.emptyTitle, { color: colors.text }]}>No hay tareas disponibles</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              {activeTab === "available"
                ? "No hay trabajos disponibles en este momento"
                : "No tienes tareas en esta categoría"}
            </Text>
          </View>
        ) : (
          filteredTasks.map((task) => (
            <TouchableOpacity
              key={task.id}
              style={[styles.taskCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
              onPress={() => navigation.navigate("TaskDetail", { taskId: task.id })}
            >
              <View style={styles.taskHeader}>
                <Text style={[styles.taskTitle, { color: colors.text }]}>{task.title}</Text>
                <View style={[styles.urgencyBadge, { backgroundColor: getUrgencyColor(task.urgency) }]}>
                  <Text style={styles.urgencyText}>{getUrgencyText(task.urgency)}</Text>
                </View>
              </View>

              <Text style={[styles.taskDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                {task.description}
              </Text>

              <View style={styles.taskMeta}>
                <View style={styles.taskInfo}>
                  <Text style={[styles.taskCategory, { color: colors.primary }]}>📍 {task.category}</Text>
                  <Text style={[styles.taskLocation, { color: colors.textSecondary }]}>
                    {task.location.address.split(",")[0]}
                  </Text>
                  <Text style={[styles.taskTime, { color: colors.textSecondary }]}>⏱️ ~{task.estimatedDuration}h</Text>
                </View>

                <View style={styles.taskBudget}>
                  <Text style={[styles.budgetText, { color: colors.success }]}>
                    ${task.budget.min} - ${task.budget.max}
                  </Text>
                  <Text style={[styles.statusText, { color: colors.textSecondary }]}>
                    {task.status === "open"
                      ? "Disponible"
                      : task.status === "assigned"
                        ? "Asignada"
                        : task.status === "in_progress"
                          ? "En progreso"
                          : task.status === "completed"
                            ? "Completada"
                            : "Cancelada"}
                  </Text>
                </View>
              </View>

              {task.requirements.length > 0 && (
                <View style={styles.requirements}>
                  <Text style={[styles.requirementsTitle, { color: colors.textSecondary }]}>Requisitos:</Text>
                  <Text style={[styles.requirementsText, { color: colors.text }]} numberOfLines={1}>
                    {task.requirements.join(" • ")}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))
        )}
      </ScrollView>

      {/* Create Task Button for Clients */}
      {user?.role === "client" && (
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate("CreateTask")}
        >
          <Text style={styles.fabText}>+</Text>
        </TouchableOpacity>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 15,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 12,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  tabsContainer: {
    paddingVertical: 10,
  },
  tabs: {
    paddingHorizontal: 20,
  },
  tab: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 10,
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
  },
  categoriesContainer: {
    paddingVertical: 10,
  },
  categories: {
    paddingHorizontal: 20,
  },
  categoryChip: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 10,
  },
  categoryChipText: {
    fontSize: 12,
    fontWeight: "500",
  },
  tasksList: {
    flex: 1,
  },
  tasksContent: {
    padding: 20,
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyEmoji: {
    fontSize: 60,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 22,
  },
  taskCard: {
    padding: 20,
    borderRadius: 15,
    borderWidth: 1,
    marginBottom: 15,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 10,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: "bold",
    flex: 1,
    marginRight: 15,
  },
  urgencyBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  urgencyText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "bold",
  },
  taskDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 15,
  },
  taskMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    marginBottom: 10,
  },
  taskInfo: {
    flex: 1,
  },
  taskCategory: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
  },
  taskLocation: {
    fontSize: 12,
    marginBottom: 2,
  },
  taskTime: {
    fontSize: 12,
  },
  taskBudget: {
    alignItems: "flex-end",
  },
  budgetText: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 2,
  },
  statusText: {
    fontSize: 12,
  },
  requirements: {
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
    paddingTop: 10,
  },
  requirementsTitle: {
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 4,
  },
  requirementsText: {
    fontSize: 12,
  },
  fab: {
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  fabText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "bold",
  },
})
