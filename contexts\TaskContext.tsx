"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

export interface Task {
  id: string
  title: string
  description: string
  category: string
  location: {
    address: string
    coordinates: {
      latitude: number
      longitude: number
    }
  }
  budget: {
    min: number
    max: number
  }
  urgency: "low" | "medium" | "high"
  status: "open" | "assigned" | "in_progress" | "completed" | "cancelled"
  clientId: string
  workerId?: string
  createdAt: Date
  scheduledFor?: Date
  estimatedDuration: number
  requirements: string[]
  images?: string[]
}

interface TaskContextType {
  tasks: Task[]
  myTasks: Task[]
  availableTasks: Task[]
  createTask: (task: Omit<Task, "id" | "createdAt" | "status">) => Promise<void>
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>
  assignTask: (taskId: string, workerId: string) => Promise<void>
  getTaskById: (id: string) => Task | undefined
  getTasksByCategory: (category: string) => Task[]
}

const TaskContext = createContext<TaskContextType | undefined>(undefined)

const mockTasks: Task[] = [
  {
    id: "1",
    title: "Reparación de grifo de cocina",
    description: "El grifo de la cocina gotea constantemente y necesita reparación urgente.",
    category: "Plomería",
    location: {
      address: "Av. Libertador 1234, Buenos Aires",
      coordinates: { latitude: -34.6037, longitude: -58.3816 },
    },
    budget: { min: 50, max: 100 },
    urgency: "high",
    status: "open",
    clientId: "client1",
    createdAt: new Date(),
    estimatedDuration: 2,
    requirements: ["Herramientas propias", "Experiencia en plomería", "Disponibilidad inmediata"],
  },
  {
    id: "2",
    title: "Limpieza profunda de apartamento",
    description: "Necesito limpieza completa de apartamento de 2 habitaciones antes de mudanza.",
    category: "Limpieza",
    location: {
      address: "Calle Corrientes 5678, Buenos Aires",
      coordinates: { latitude: -34.6118, longitude: -58.396 },
    },
    budget: { min: 80, max: 150 },
    urgency: "medium",
    status: "open",
    clientId: "client2",
    createdAt: new Date(),
    scheduledFor: new Date(Date.now() + 86400000),
    estimatedDuration: 4,
    requirements: ["Productos de limpieza incluidos", "Referencias verificables"],
  },
  {
    id: "3",
    title: "Instalación de lámpara de techo",
    description: "Instalar lámpara nueva en sala de estar, incluye conexión eléctrica.",
    category: "Electricidad",
    location: {
      address: "Av. Santa Fe 9012, Buenos Aires",
      coordinates: { latitude: -34.5956, longitude: -58.3772 },
    },
    budget: { min: 40, max: 80 },
    urgency: "low",
    status: "assigned",
    clientId: "client3",
    workerId: "worker1",
    createdAt: new Date(),
    estimatedDuration: 1,
    requirements: ["Certificación eléctrica", "Seguro de responsabilidad civil"],
  },
]

export function TaskProvider({ children }: { children: React.ReactNode }) {
  const [tasks, setTasks] = useState<Task[]>(mockTasks)

  useEffect(() => {
    loadTasks()
  }, [])

  const loadTasks = async () => {
    try {
      const tasksData = await AsyncStorage.getItem("tasks")
      if (tasksData) {
        const parsedTasks = JSON.parse(tasksData).map((task: any) => ({
          ...task,
          createdAt: new Date(task.createdAt),
          scheduledFor: task.scheduledFor ? new Date(task.scheduledFor) : undefined,
        }))
        setTasks([...mockTasks, ...parsedTasks])
      }
    } catch (error) {
      console.error("Error loading tasks:", error)
    }
  }

  const saveTasks = async (newTasks: Task[]) => {
    try {
      const tasksToSave = newTasks.filter((task) => !mockTasks.find((mock) => mock.id === task.id))
      await AsyncStorage.setItem("tasks", JSON.stringify(tasksToSave))
    } catch (error) {
      console.error("Error saving tasks:", error)
    }
  }

  const createTask = async (taskData: Omit<Task, "id" | "createdAt" | "status">) => {
    const newTask: Task = {
      ...taskData,
      id: Date.now().toString(),
      createdAt: new Date(),
      status: "open",
    }
    const updatedTasks = [...tasks, newTask]
    setTasks(updatedTasks)
    await saveTasks(updatedTasks)
  }

  const updateTask = async (id: string, updates: Partial<Task>) => {
    const updatedTasks = tasks.map((task) => (task.id === id ? { ...task, ...updates } : task))
    setTasks(updatedTasks)
    await saveTasks(updatedTasks)
  }

  const assignTask = async (taskId: string, workerId: string) => {
    await updateTask(taskId, { workerId, status: "assigned" })
  }

  const getTaskById = (id: string): Task | undefined => {
    return tasks.find((task) => task.id === id)
  }

  const getTasksByCategory = (category: string): Task[] => {
    return tasks.filter((task) => task.category === category)
  }

  const myTasks = tasks.filter((task) => task.clientId === "current_user" || task.workerId === "current_user")
  const availableTasks = tasks.filter((task) => task.status === "open")

  return (
    <TaskContext.Provider
      value={{
        tasks,
        myTasks,
        availableTasks,
        createTask,
        updateTask,
        assignTask,
        getTaskById,
        getTasksByCategory,
      }}
    >
      {children}
    </TaskContext.Provider>
  )
}

export function useTasks() {
  const context = useContext(TaskContext)
  if (!context) {
    throw new Error("useTasks must be used within a TaskProvider")
  }
  return context
}
