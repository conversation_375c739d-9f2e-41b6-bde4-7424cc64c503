"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView, Alert } from "react-native"
import { useTheme } from "../contexts/ThemeContext"

export default function BillingScreen({ navigation }: any) {
  const { colors } = useTheme()
  const [activeTab, setActiveTab] = useState<"invoices" | "payments" | "reports">("invoices")

  const mockInvoices = [
    {
      id: "INV-001",
      client: "<PERSON> González",
      amount: 150,
      status: "paid",
      date: "2024-01-15",
      dueDate: "2024-01-30",
      description: "Reparación de plomería",
    },
    {
      id: "INV-002",
      client: "Carlos Mendoza",
      amount: 200,
      status: "pending",
      date: "2024-01-14",
      dueDate: "2024-01-29",
      description: "Limpieza profunda",
    },
    {
      id: "INV-003",
      client: "<PERSON> Rodríguez",
      amount: 300,
      status: "overdue",
      date: "2024-01-10",
      dueDate: "2024-01-25",
      description: "Instalación eléctrica",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return colors.success
      case "pending":
        return colors.warning
      case "overdue":
        return colors.error
      default:
        return colors.textSecondary
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "paid":
        return "Pagada"
      case "pending":
        return "Pendiente"
      case "overdue":
        return "Vencida"
      default:
        return "Desconocido"
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backIcon, { color: colors.text }]}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Facturación</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={() => Alert.alert("Función Premium", "Crear facturas automáticas - Próximamente")}
        >
          <Text style={styles.addIcon}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Summary Cards */}
      <View style={styles.summaryContainer}>
        <View style={[styles.summaryCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.summaryAmount, { color: colors.success }]}>$650</Text>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Facturado</Text>
        </View>
        <View style={[styles.summaryCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.summaryAmount, { color: colors.warning }]}>$500</Text>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Pendiente</Text>
        </View>
        <View style={[styles.summaryCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.summaryAmount, { color: colors.primary }]}>$150</Text>
          <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Este Mes</Text>
        </View>
      </View>

      {/* Tabs */}
      <View style={[styles.tabsContainer, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === "invoices" ? colors.primary : "transparent" }]}
          onPress={() => setActiveTab("invoices")}
        >
          <Text style={[styles.tabText, { color: activeTab === "invoices" ? "#FFFFFF" : colors.text }]}>Facturas</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === "payments" ? colors.primary : "transparent" }]}
          onPress={() => setActiveTab("payments")}
        >
          <Text style={[styles.tabText, { color: activeTab === "payments" ? "#FFFFFF" : colors.text }]}>Pagos</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === "reports" ? colors.primary : "transparent" }]}
          onPress={() => setActiveTab("reports")}
        >
          <Text style={[styles.tabText, { color: activeTab === "reports" ? "#FFFFFF" : colors.text }]}>Reportes</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === "invoices" &&
          mockInvoices.map((invoice) => (
            <TouchableOpacity
              key={invoice.id}
              style={[styles.invoiceCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
              onPress={() => Alert.alert("Ver Factura", `Factura ${invoice.id} - Función Premium`)}
            >
              <View style={styles.invoiceHeader}>
                <View style={styles.invoiceTitleContainer}>
                  <Text style={[styles.invoiceId, { color: colors.text }]}>{invoice.id}</Text>
                  <Text style={[styles.invoiceClient, { color: colors.textSecondary }]}>{invoice.client}</Text>
                  <Text style={[styles.invoiceDescription, { color: colors.textSecondary }]}>
                    {invoice.description}
                  </Text>
                </View>
                <View style={styles.invoiceAmount}>
                  <Text style={[styles.amountText, { color: colors.success }]}>${invoice.amount}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(invoice.status) + "15" }]}>
                    <Text style={[styles.statusText, { color: getStatusColor(invoice.status) }]}>
                      {getStatusText(invoice.status)}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.invoiceFooter}>
                <View style={styles.invoiceDates}>
                  <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>Emitida: {invoice.date}</Text>
                  <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>Vence: {invoice.dueDate}</Text>
                </View>
                <View style={styles.invoiceActions}>
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: colors.primary + "15" }]}
                    onPress={() => Alert.alert("Descargar", "Descargar PDF")}
                  >
                    <Text style={[styles.actionButtonText, { color: colors.primary }]}>PDF</Text>
                  </TouchableOpacity>
                  {invoice.status === "pending" && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: colors.success + "15" }]}
                      onPress={() => Alert.alert("Recordatorio", "Enviar recordatorio de pago")}
                    >
                      <Text style={[styles.actionButtonText, { color: colors.success }]}>Recordar</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}

        {activeTab === "payments" && (
          <View style={[styles.emptyState, { backgroundColor: colors.surface }]}>
            <Text style={styles.emptyIcon}>💳</Text>
            <Text style={[styles.emptyTitle, { color: colors.text }]}>Historial de Pagos</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              Aquí verás todos los pagos recibidos y procesados
            </Text>
          </View>
        )}

        {activeTab === "reports" && (
          <View style={[styles.emptyState, { backgroundColor: colors.surface }]}>
            <Text style={styles.emptyIcon}>📊</Text>
            <Text style={[styles.emptyTitle, { color: colors.text }]}>Reportes Financieros</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              Genera reportes detallados de tus ingresos y gastos
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  backIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  addIcon: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  summaryContainer: {
    flexDirection: "row",
    paddingHorizontal: 20,
    paddingVertical: 10,
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  summaryAmount: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: "500",
  },
  tabsContainer: {
    flexDirection: "row",
    padding: 20,
    paddingTop: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 4,
    alignItems: "center",
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    padding: 20,
  },
  invoiceCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  invoiceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  invoiceTitleContainer: {
    flex: 1,
    marginRight: 16,
  },
  invoiceId: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  invoiceClient: {
    fontSize: 14,
    marginBottom: 4,
  },
  invoiceDescription: {
    fontSize: 12,
  },
  invoiceAmount: {
    alignItems: "flex-end",
  },
  amountText: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  invoiceFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  invoiceDates: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  invoiceActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: "600",
  },
  emptyState: {
    padding: 40,
    borderRadius: 12,
    alignItems: "center",
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
})
