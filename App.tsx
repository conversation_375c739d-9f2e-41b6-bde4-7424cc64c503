"use client"

import { useEffect, useState } from "react"
import { NavigationContainer } from "@react-navigation/native"
import { createStackNavigator } from "@react-navigation/stack"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { createDrawerNavigator } from "@react-navigation/drawer"
import { StatusBar } from "expo-status-bar"
import { View, Text } from "react-native"

import { ThemeProvider } from "./contexts/ThemeContext"
import { UserProvider } from "./contexts/UserContext"
import { TaskProvider } from "./contexts/TaskContext"
import { GoalsProvider } from "./contexts/GoalsContext"

import SplashScreen from "./screens/SplashScreen"
import LoginScreen from "./screens/LoginScreen"
import RegisterScreen from "./screens/RegisterScreen"
import HomeScreen from "./screens/HomeScreen"
import DashboardScreen from "./screens/DashboardScreen"
import TasksScreen from "./screens/TasksScreen"
import ProfileScreen from "./screens/ProfileScreen"
import ChatScreen from "./screens/ChatScreen"
import TuAmigoScreen from "./screens/TuAmigoScreen"
import GoalsScreen from "./screens/GoalsScreen"
import TaskDetailScreen from "./screens/TaskDetailScreen"
import CreateTaskScreen from "./screens/CreateTaskScreen"
import MapScreen from "./screens/MapScreen"
import QuotesScreen from "./screens/QuotesScreen"
import ContractsScreen from "./screens/ContractsScreen"
import BillingScreen from "./screens/BillingScreen"
import ReportsScreen from "./screens/ReportsScreen"
import CertificationsScreen from "./screens/CertificationsScreen"
import SettingsScreen from "./screens/SettingsScreen"
import SupportScreen from "./screens/SupportScreen"
import CustomDrawerContent from "./components/CustomDrawerContent"

import { useUser } from "./contexts/UserContext"
import { useTheme } from "./contexts/ThemeContext"

const Stack = createStackNavigator()
const Tab = createBottomTabNavigator()
const Drawer = createDrawerNavigator()

function TabNavigator() {
  const { user } = useUser()
  const { colors } = useTheme()

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: 8,
          height: 70,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 10,
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarLabelStyle: {
          fontSize: 11,
          fontWeight: "600",
          marginTop: 4,
        },
        headerStyle: {
          backgroundColor: colors.surface,
          shadowColor: colors.border,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 2,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontWeight: "600",
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: "Inicio",
          tabBarIcon: ({ color }) => (
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 20, color }}>🏠</Text>
            </View>
          ),
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          title: "Panel",
          tabBarIcon: ({ color }) => (
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 20, color }}>📊</Text>
            </View>
          ),
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Tasks"
        component={TasksScreen}
        options={{
          title: user?.currentMode === "client" ? "Proyectos" : "Trabajos",
          tabBarIcon: ({ color }) => (
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 20, color }}>📋</Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Goals"
        component={GoalsScreen}
        options={{
          title: "Objetivos",
          tabBarIcon: ({ color }) => (
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 20, color }}>🎯</Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="TuAmigo"
        component={TuAmigoScreen}
        options={{
          title: "Asistente",
          tabBarIcon: ({ color }) => (
            <View style={{ justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 20, color }}>🤖</Text>
            </View>
          ),
        }}
      />
    </Tab.Navigator>
  )
}

function DrawerNavigator() {
  const { colors } = useTheme()

  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: colors.surface,
          width: 300,
        },
        drawerActiveTintColor: colors.primary,
        drawerInactiveTintColor: colors.textSecondary,
        drawerLabelStyle: {
          fontSize: 16,
          fontWeight: "500",
        },
      }}
    >
      <Drawer.Screen name="MainTabs" component={TabNavigator} />
    </Drawer.Navigator>
  )
}

function AppNavigator() {
  const { user, isLoading } = useUser()
  const { theme, colors } = useTheme()
  const [showSplash, setShowSplash] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSplash(false)
    }, 2000)
    return () => clearTimeout(timer)
  }, [])

  if (showSplash || isLoading) {
    return <SplashScreen />
  }

  return (
    <NavigationContainer
      theme={{
        dark: theme === "dark",
        colors: {
          primary: colors.primary,
          background: colors.background,
          card: colors.surface,
          text: colors.text,
          border: colors.border,
          notification: colors.error,
        },
      }}
    >
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: colors.surface,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          },
          headerTintColor: colors.text,
          headerTitleStyle: {
            fontWeight: "600",
          },
          headerBackTitleVisible: false,
        }}
      >
        {!user ? (
          <>
            <Stack.Screen name="Login" component={LoginScreen} options={{ headerShown: false }} />
            <Stack.Screen name="Register" component={RegisterScreen} options={{ title: "Crear Cuenta" }} />
          </>
        ) : (
          <>
            <Stack.Screen name="Main" component={DrawerNavigator} options={{ headerShown: false }} />
            <Stack.Screen name="CreateTask" component={CreateTaskScreen} options={{ title: "Nuevo Proyecto" }} />
            <Stack.Screen name="TaskDetail" component={TaskDetailScreen} options={{ title: "Detalles del Proyecto" }} />
            <Stack.Screen name="Chat" component={ChatScreen} options={{ title: "Mensajes" }} />
            <Stack.Screen name="Map" component={MapScreen} options={{ title: "Ubicación" }} />
            <Stack.Screen name="Profile" component={ProfileScreen} options={{ title: "Mi Perfil" }} />
            <Stack.Screen name="Quotes" component={QuotesScreen} options={{ title: "Cotizaciones" }} />
            <Stack.Screen name="Contracts" component={ContractsScreen} options={{ title: "Contratos" }} />
            <Stack.Screen name="Billing" component={BillingScreen} options={{ title: "Facturación" }} />
            <Stack.Screen name="Reports" component={ReportsScreen} options={{ title: "Reportes" }} />
            <Stack.Screen
              name="Certifications"
              component={CertificationsScreen}
              options={{ title: "Certificaciones" }}
            />
            <Stack.Screen name="Settings" component={SettingsScreen} options={{ title: "Configuración" }} />
            <Stack.Screen name="Support" component={SupportScreen} options={{ title: "Soporte" }} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  )
}

export default function App() {
  return (
    <ThemeProvider>
      <UserProvider>
        <TaskProvider>
          <GoalsProvider>
            <AppNavigator />
            <StatusBar style="auto" />
          </GoalsProvider>
        </TaskProvider>
      </UserProvider>
    </ThemeProvider>
  )
}
