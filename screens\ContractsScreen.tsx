"use client"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView, Alert } from "react-native"
import { useTheme } from "../contexts/ThemeContext"

export default function ContractsScreen({ navigation }: any) {
  const { colors } = useTheme()

  const mockContracts = [
    {
      id: "1",
      title: "Contrato de Plomería",
      client: "<PERSON>",
      amount: 150,
      status: "active",
      startDate: "2024-01-15",
      endDate: "2024-01-20",
      progress: 75,
    },
    {
      id: "2",
      title: "Contrato de Limpieza",
      client: "<PERSON>",
      amount: 200,
      status: "completed",
      startDate: "2024-01-10",
      endDate: "2024-01-15",
      progress: 100,
    },
    {
      id: "3",
      title: "Contrato de Electricidad",
      client: "<PERSON> Rodríguez",
      amount: 300,
      status: "pending",
      startDate: "2024-01-20",
      endDate: "2024-01-25",
      progress: 0,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return colors.primary
      case "completed":
        return colors.success
      case "pending":
        return colors.warning
      default:
        return colors.textSecondary
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Activo"
      case "completed":
        return "Completado"
      case "pending":
        return "Pendiente"
      default:
        return "Desconocido"
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backIcon, { color: colors.text }]}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Contratos</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={() => Alert.alert("Función Premium", "Crear contratos digitales - Próximamente")}
        >
          <Text style={styles.addIcon}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Contracts List */}
      <ScrollView style={styles.contractsList} showsVerticalScrollIndicator={false}>
        {mockContracts.map((contract) => (
          <TouchableOpacity
            key={contract.id}
            style={[styles.contractCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
            onPress={() => Alert.alert("Ver Contrato", "Detalles del contrato - Función Premium")}
          >
            <View style={styles.contractHeader}>
              <View style={styles.contractTitleContainer}>
                <Text style={[styles.contractTitle, { color: colors.text }]}>{contract.title}</Text>
                <Text style={[styles.contractClient, { color: colors.textSecondary }]}>{contract.client}</Text>
              </View>
              <View style={styles.contractAmount}>
                <Text style={[styles.amountText, { color: colors.success }]}>${contract.amount}</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(contract.status) + "15" }]}>
                  <Text style={[styles.statusText, { color: getStatusColor(contract.status) }]}>
                    {getStatusText(contract.status)}
                  </Text>
                </View>
              </View>
            </View>

            {/* Progress Bar */}
            <View style={styles.progressSection}>
              <View style={styles.progressHeader}>
                <Text style={[styles.progressLabel, { color: colors.text }]}>Progreso</Text>
                <Text style={[styles.progressPercentage, { color: colors.primary }]}>{contract.progress}%</Text>
              </View>
              <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      backgroundColor: contract.progress === 100 ? colors.success : colors.primary,
                      width: `${contract.progress}%`,
                    },
                  ]}
                />
              </View>
            </View>

            <View style={styles.contractFooter}>
              <View style={styles.contractDates}>
                <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>
                  {contract.startDate} - {contract.endDate}
                </Text>
              </View>
              <View style={styles.contractActions}>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: colors.primary + "15" }]}
                  onPress={() => Alert.alert("Ver", "Ver contrato completo")}
                >
                  <Text style={[styles.actionButtonText, { color: colors.primary }]}>Ver</Text>
                </TouchableOpacity>
                {contract.status === "active" && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: colors.success + "15" }]}
                    onPress={() => Alert.alert("Actualizar", "Actualizar progreso del contrato")}
                  >
                    <Text style={[styles.actionButtonText, { color: colors.success }]}>Actualizar</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  backIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  addIcon: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  contractsList: {
    flex: 1,
    padding: 20,
  },
  contractCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  contractHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  contractTitleContainer: {
    flex: 1,
    marginRight: 16,
  },
  contractTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  contractClient: {
    fontSize: 14,
  },
  contractAmount: {
    alignItems: "flex-end",
  },
  amountText: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: "600",
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  progressFill: {
    height: "100%",
    borderRadius: 3,
  },
  contractFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  contractDates: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 12,
  },
  contractActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: "600",
  },
})
