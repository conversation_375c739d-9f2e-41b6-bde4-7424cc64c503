"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Modal } from "react-native"
import DateTimePicker from "@react-native-community/datetimepicker"
import { useTheme } from "../contexts/ThemeContext"
import { useGoals, type Goal } from "../contexts/GoalsContext"
import { useUser } from "../contexts/UserContext"

export default function GoalsScreen() {
  const { colors } = useTheme()
  const { user } = useUser()
  const { goals, addGoal, updateGoal, deleteGoal, getProgress } = useGoals()
  const [showModal, setShowModal] = useState(false)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [newGoal, setNewGoal] = useState({
    title: "",
    targetAmount: "",
    type: "income" as "income" | "tasks" | "spending",
    deadline: new Date(),
  })

  const handleAddGoal = async () => {
    if (!newGoal.title || !newGoal.targetAmount) {
      Alert.alert("Error", "Por favor completa todos los campos")
      return
    }

    await addGoal({
      title: newGoal.title,
      targetAmount: Number.parseFloat(newGoal.targetAmount),
      currentAmount: 0,
      type: newGoal.type,
      deadline: newGoal.deadline,
    })

    setNewGoal({
      title: "",
      targetAmount: "",
      type: "income",
      deadline: new Date(),
    })
    setShowModal(false)
    Alert.alert("¡Genial!", "Meta creada exitosamente")
  }

  const handleUpdateProgress = (goal: Goal) => {
    Alert.prompt(
      "Actualizar Progreso",
      `Progreso actual: $${goal.currentAmount}`,
      [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Actualizar",
          onPress: (value) => {
            if (value) {
              const newAmount = Number.parseFloat(value)
              if (!isNaN(newAmount)) {
                updateGoal(goal.id, { currentAmount: newAmount })
              }
            }
          },
        },
      ],
      "plain-text",
      goal.currentAmount.toString(),
    )
  }

  const getGoalTypeIcon = (type: string) => {
    switch (type) {
      case "income":
        return "💰"
      case "tasks":
        return "✅"
      case "spending":
        return "🛒"
      default:
        return "🎯"
    }
  }

  const getGoalTypeLabel = (type: string) => {
    switch (type) {
      case "income":
        return "Ingresos"
      case "tasks":
        return "Tareas"
      case "spending":
        return "Gastos"
      default:
        return "Meta"
    }
  }

  const getDaysRemaining = (deadline: Date) => {
    const today = new Date()
    const diffTime = deadline.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>Mis Metas 🎯</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {user?.role === "worker"
              ? "Establece tus objetivos de ingresos y tareas"
              : "Controla tus gastos y objetivos"}
          </Text>
        </View>

        {goals.length === 0 ? (
          <View style={[styles.emptyState, { backgroundColor: colors.surface }]}>
            <Text style={styles.emptyEmoji}>🎯</Text>
            <Text style={[styles.emptyTitle, { color: colors.text }]}>No tienes metas aún</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              Crea tu primera meta para comenzar a alcanzar tus objetivos
            </Text>
          </View>
        ) : (
          <View style={styles.goalsContainer}>
            {goals.map((goal) => {
              const progress = getProgress(goal.id)
              const daysRemaining = getDaysRemaining(goal.deadline)

              return (
                <View
                  key={goal.id}
                  style={[styles.goalCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
                >
                  <View style={styles.goalHeader}>
                    <View style={styles.goalTitleContainer}>
                      <Text style={styles.goalIcon}>{getGoalTypeIcon(goal.type)}</Text>
                      <View>
                        <Text style={[styles.goalTitle, { color: colors.text }]}>{goal.title}</Text>
                        <Text style={[styles.goalType, { color: colors.textSecondary }]}>
                          {getGoalTypeLabel(goal.type)}
                        </Text>
                      </View>
                    </View>

                    <TouchableOpacity
                      onPress={() => handleUpdateProgress(goal)}
                      style={[styles.updateButton, { backgroundColor: colors.primary }]}
                    >
                      <Text style={styles.updateButtonText}>Actualizar</Text>
                    </TouchableOpacity>
                  </View>

                  <View style={styles.progressSection}>
                    <View style={styles.progressInfo}>
                      <Text style={[styles.progressText, { color: colors.text }]}>
                        ${goal.currentAmount} / ${goal.targetAmount}
                      </Text>
                      <Text style={[styles.progressPercentage, { color: colors.primary }]}>{progress.toFixed(0)}%</Text>
                    </View>

                    <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            backgroundColor: progress >= 100 ? colors.success : colors.primary,
                            width: `${Math.min(progress, 100)}%`,
                          },
                        ]}
                      />
                    </View>
                  </View>

                  <View style={styles.goalFooter}>
                    <Text style={[styles.deadline, { color: colors.textSecondary }]}>
                      {daysRemaining > 0
                        ? `${daysRemaining} días restantes`
                        : daysRemaining === 0
                          ? "Vence hoy"
                          : `Venció hace ${Math.abs(daysRemaining)} días`}
                    </Text>

                    <TouchableOpacity
                      onPress={() => {
                        Alert.alert("Eliminar Meta", "¿Estás seguro que quieres eliminar esta meta?", [
                          { text: "Cancelar", style: "cancel" },
                          { text: "Eliminar", onPress: () => deleteGoal(goal.id), style: "destructive" },
                        ])
                      }}
                      style={styles.deleteButton}
                    >
                      <Text style={[styles.deleteButtonText, { color: colors.error }]}>Eliminar</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )
            })}
          </View>
        )}
      </ScrollView>

      <TouchableOpacity style={[styles.fab, { backgroundColor: colors.primary }]} onPress={() => setShowModal(true)}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>

      <Modal visible={showModal} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={[styles.modalCancel, { color: colors.primary }]}>Cancelar</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Nueva Meta</Text>
            <TouchableOpacity onPress={handleAddGoal}>
              <Text style={[styles.modalSave, { color: colors.primary }]}>Guardar</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Título de la meta</Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="Ej: Ganar $500 este mes"
                placeholderTextColor={colors.textSecondary}
                value={newGoal.title}
                onChangeText={(text) => setNewGoal({ ...newGoal, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Tipo de meta</Text>
              <View style={styles.typeButtons}>
                {(["income", "tasks", "spending"] as const).map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.typeButton,
                      {
                        backgroundColor: newGoal.type === type ? colors.primary : colors.surface,
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={() => setNewGoal({ ...newGoal, type })}
                  >
                    <Text style={styles.typeEmoji}>{getGoalTypeIcon(type)}</Text>
                    <Text style={[styles.typeText, { color: newGoal.type === type ? "#FFFFFF" : colors.text }]}>
                      {getGoalTypeLabel(type)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Cantidad objetivo</Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="0"
                placeholderTextColor={colors.textSecondary}
                value={newGoal.targetAmount}
                onChangeText={(text) => setNewGoal({ ...newGoal, targetAmount: text })}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>Fecha límite</Text>
              <TouchableOpacity
                style={[styles.dateButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={[styles.dateText, { color: colors.text }]}>{newGoal.deadline.toLocaleDateString()}</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>

          {showDatePicker && (
            <DateTimePicker
              value={newGoal.deadline}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowDatePicker(false)
                if (selectedDate) {
                  setNewGoal({ ...newGoal, deadline: selectedDate })
                }
              }}
            />
          )}
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
  },
  emptyState: {
    margin: 20,
    padding: 40,
    borderRadius: 15,
    alignItems: "center",
  },
  emptyEmoji: {
    fontSize: 60,
    marginBottom: 15,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 22,
  },
  goalsContainer: {
    padding: 20,
  },
  goalCard: {
    padding: 20,
    borderRadius: 15,
    borderWidth: 1,
    marginBottom: 15,
  },
  goalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  goalTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  goalIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  goalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 2,
  },
  goalType: {
    fontSize: 14,
  },
  updateButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  updateButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  progressSection: {
    marginBottom: 15,
  },
  progressInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  progressText: {
    fontSize: 16,
    fontWeight: "600",
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: "bold",
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressFill: {
    height: "100%",
    borderRadius: 4,
  },
  goalFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  deadline: {
    fontSize: 14,
  },
  deleteButton: {
    padding: 5,
  },
  deleteButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  fab: {
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  fabText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "bold",
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
  },
  modalCancel: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  modalSave: {
    fontSize: 16,
    fontWeight: "600",
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
  },
  typeButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  typeButton: {
    flex: 1,
    alignItems: "center",
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    marginHorizontal: 5,
  },
  typeEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  typeText: {
    fontSize: 14,
    fontWeight: "600",
  },
  dateButton: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
  },
  dateText: {
    fontSize: 16,
  },
})
