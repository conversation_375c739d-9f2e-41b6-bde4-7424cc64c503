"use client"

import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView, Switch, Alert } from "react-native"
import { useTheme } from "../contexts/ThemeContext"

export default function SettingsScreen({ navigation }: any) {
  const { colors, theme, toggleTheme } = useTheme()

  const settingsOptions = [
    {
      section: "Cuenta",
      items: [
        { title: "Información Personal", icon: "👤", action: () => navigation.navigate("Profile") },
        {
          title: "Verificación de Identidad",
          icon: "🆔",
          action: () => Alert.alert("Verificación", "Función Premium"),
        },
        { title: "Métodos de Pago", icon: "💳", action: () => Alert.alert("Pagos", "Función Premium") },
        {
          title: "Configuración de Privacidad",
          icon: "🔒",
          action: () => Alert.alert("Privacidad", "Función Premium"),
        },
      ],
    },
    {
      section: "Notificaciones",
      items: [
        { title: "Nuevos Proyectos", icon: "📋", toggle: true, value: true },
        { title: "Mensajes", icon: "💬", toggle: true, value: true },
        { title: "Pagos", icon: "💰", toggle: true, value: false },
        { title: "Promociones", icon: "🎉", toggle: true, value: false },
      ],
    },
    {
      section: "Aplicación",
      items: [
        { title: "Idioma", icon: "🌐", action: () => Alert.alert("Idioma", "Español (predeterminado)") },
        {
          title: "Modo Oscuro",
          icon: theme === "dark" ? "☀️" : "🌙",
          toggle: true,
          value: theme === "dark",
          action: toggleTheme,
        },
        { title: "Caché", icon: "🗂️", action: () => Alert.alert("Caché", "Limpiar datos temporales") },
        {
          title: "Actualizaciones",
          icon: "🔄",
          action: () => Alert.alert("Actualizaciones", "Buscar actualizaciones"),
        },
      ],
    },
  ]

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backIcon, { color: colors.text }]}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Configuración</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {settingsOptions.map((section, sectionIndex) => (
          <View key={sectionIndex} style={[styles.section, { backgroundColor: colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>{section.section}</Text>
            {section.items.map((item, itemIndex) => (
              <TouchableOpacity
                key={itemIndex}
                style={[styles.settingItem, { borderBottomColor: colors.border }]}
                onPress={item.action}
                disabled={item.toggle}
              >
                <View style={styles.settingLeft}>
                  <Text style={styles.settingIcon}>{item.icon}</Text>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>{item.title}</Text>
                </View>
                {item.toggle ? (
                  <Switch
                    value={item.value}
                    onValueChange={item.action}
                    trackColor={{ false: colors.border, true: colors.primary }}
                    thumbColor={item.value ? "#FFFFFF" : colors.textSecondary}
                  />
                ) : (
                  <Text style={[styles.settingArrow, { color: colors.textSecondary }]}>›</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        ))}

        {/* Support Section */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>Soporte</Text>
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => navigation.navigate("Support")}
          >
            <View style={styles.settingLeft}>
              <Text style={styles.settingIcon}>🆘</Text>
              <Text style={[styles.settingTitle, { color: colors.text }]}>Centro de Ayuda</Text>
            </View>
            <Text style={[styles.settingArrow, { color: colors.textSecondary }]}>›</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => Alert.alert("Contacto", "<EMAIL>")}
          >
            <View style={styles.settingLeft}>
              <Text style={styles.settingIcon}>📧</Text>
              <Text style={[styles.settingTitle, { color: colors.text }]}>Contactar Soporte</Text>
            </View>
            <Text style={[styles.settingArrow, { color: colors.textSecondary }]}>›</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.border }]}
            onPress={() => Alert.alert("Versión", "TaskApp v1.0.0")}
          >
            <View style={styles.settingLeft}>
              <Text style={styles.settingIcon}>ℹ️</Text>
              <Text style={[styles.settingTitle, { color: colors.text }]}>Acerca de</Text>
            </View>
            <Text style={[styles.settingArrow, { color: colors.textSecondary }]}>›</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  backIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    borderRadius: 12,
    marginBottom: 20,
    overflow: "hidden",
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 1,
    padding: 16,
    paddingBottom: 8,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 0.5,
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  settingIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 24,
    textAlign: "center",
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  settingArrow: {
    fontSize: 18,
    fontWeight: "300",
  },
})
