"use client"
import { View, Text, StyleSheet, TouchableOpacity, Image, Alert } from "react-native"
import { DrawerContentScrollView } from "@react-navigation/drawer"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

export default function CustomDrawerContent(props: any) {
  const { colors, theme, toggleTheme } = useTheme()
  const { user, switchMode, logout } = useUser()

  const menuSections = [
    {
      title: "Principal",
      items: [
        { name: "Dashboard", icon: "📊", screen: "Dashboard", premium: false },
        { name: "Proyectos", icon: "📋", screen: "Tasks", premium: false },
        { name: "<PERSON><PERSON><PERSON><PERSON>", icon: "💬", screen: "Chat", premium: false },
        { name: "Calendario", icon: "📅", screen: "Calendar", premium: true },
      ],
    },
    {
      title: "Herramientas Pro",
      items: [
        { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", icon: "💰", screen: "Quotes", premium: true },
        { name: "Contratos", icon: "📄", screen: "Contracts", premium: true },
        { name: "Facturación", icon: "🧾", screen: "Billing", premium: true },
        { name: "Reportes", icon: "📈", screen: "Reports", premium: true },
      ],
    },
    {
      title: "Crecimiento",
      items: [
        { name: "Objetivos", icon: "🎯", screen: "Goals", premium: false },
        { name: "Asistente IA", icon: "🤖", screen: "TuAmigo", premium: false },
        { name: "Certificaciones", icon: "🏆", screen: "Certifications", premium: true },
        { name: "Networking", icon: "🤝", screen: "Network", premium: true },
      ],
    },
  ]

  const handleModeSwitch = async () => {
    const newMode = user?.currentMode === "client" ? "worker" : "client"
    await switchMode(newMode)
    Alert.alert("Modo Cambiado", `Ahora estás en modo ${newMode === "client" ? "Cliente" : "Trabajador"}`)
  }

  const navigateToScreen = (screen: string, premium: boolean) => {
    if (premium && !user?.isPremium) {
      Alert.alert("Función Premium", "Esta función requiere una cuenta Premium")
    } else {
      props.navigation.navigate(screen)
    }
  }

  const handleLogout = () => {
    Alert.alert("Cerrar Sesión", "¿Estás seguro que quieres cerrar sesión?", [
      { text: "Cancelar", style: "cancel" },
      { text: "Cerrar Sesión", onPress: logout, style: "destructive" },
    ])
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.surface }]}>
      <DrawerContentScrollView {...props} contentContainerStyle={styles.scrollContent}>
        {/* User Profile Section */}
        <View style={[styles.profileSection, { borderBottomColor: colors.border }]}>
          <View style={[styles.avatar, { backgroundColor: colors.primary }]}>
            {user?.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.avatarImage} />
            ) : (
              <Text style={styles.avatarText}>{user?.name?.charAt(0).toUpperCase()}</Text>
            )}
          </View>
          <View style={styles.profileInfo}>
            <Text style={[styles.profileName, { color: colors.text }]}>{user?.name}</Text>
            <Text style={[styles.profileRole, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Cliente Profesional" : "Proveedor de Servicios"}
            </Text>
            {user?.isVerified && (
              <View style={[styles.verifiedBadge, { backgroundColor: colors.success + "15" }]}>
                <Text style={[styles.verifiedText, { color: colors.success }]}>✓ Verificado</Text>
              </View>
            )}
          </View>
        </View>

        {/* Mode Switch */}
        <TouchableOpacity
          style={[styles.modeSwitch, { backgroundColor: colors.primary + "10", borderColor: colors.primary }]}
          onPress={handleModeSwitch}
        >
          <View style={styles.modeSwitchContent}>
            <Text style={[styles.modeSwitchTitle, { color: colors.primary }]}>
              Cambiar a {user?.currentMode === "client" ? "Trabajador" : "Cliente"}
            </Text>
            <Text style={[styles.modeSwitchSubtitle, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Ofrece servicios" : "Contrata servicios"}
            </Text>
          </View>
          <Text style={[styles.modeSwitchIcon, { color: colors.primary }]}>⇄</Text>
        </TouchableOpacity>

        {/* Menu Sections */}
        {menuSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.menuSection}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>{section.title}</Text>
            {section.items.map((item, itemIndex) => (
              <TouchableOpacity
                key={itemIndex}
                style={[styles.menuItem, { borderBottomColor: colors.border }]}
                onPress={() => navigateToScreen(item.screen, item.premium)}
              >
                <View style={styles.menuItemLeft}>
                  <Text style={styles.menuItemIcon}>{item.icon}</Text>
                  <Text style={[styles.menuItemTitle, { color: colors.text }]}>{item.name}</Text>
                </View>
                <View style={styles.menuItemRight}>
                  {item.premium && (
                    <View style={[styles.premiumBadge, { backgroundColor: colors.warning + "15" }]}>
                      <Text style={[styles.premiumText, { color: colors.warning }]}>PRO</Text>
                    </View>
                  )}
                  <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        ))}

        {/* Settings Section */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>Configuración</Text>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: colors.border }]}
            onPress={() => props.navigation.navigate("Profile")}
          >
            <View style={styles.menuItemLeft}>
              <Text style={styles.menuItemIcon}>👤</Text>
              <Text style={[styles.menuItemTitle, { color: colors.text }]}>Mi Perfil</Text>
            </View>
            <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.menuItem, { borderBottomColor: colors.border }]} onPress={toggleTheme}>
            <View style={styles.menuItemLeft}>
              <Text style={styles.menuItemIcon}>{theme === "dark" ? "☀️" : "🌙"}</Text>
              <Text style={[styles.menuItemTitle, { color: colors.text }]}>
                Modo {theme === "dark" ? "Claro" : "Oscuro"}
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: colors.border }]}
            onPress={() => props.navigation.navigate("Settings")}
          >
            <View style={styles.menuItemLeft}>
              <Text style={styles.menuItemIcon}>⚙️</Text>
              <Text style={[styles.menuItemTitle, { color: colors.text }]}>Configuración</Text>
            </View>
            <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: colors.border }]}
            onPress={() => props.navigation.navigate("Support")}
          >
            <View style={styles.menuItemLeft}>
              <Text style={styles.menuItemIcon}>🆘</Text>
              <Text style={[styles.menuItemTitle, { color: colors.text }]}>Soporte</Text>
            </View>
            <Text style={[styles.menuItemArrow, { color: colors.textSecondary }]}>›</Text>
          </TouchableOpacity>
        </View>
      </DrawerContentScrollView>

      {/* Logout Button */}
      <View style={styles.logoutContainer}>
        <TouchableOpacity
          style={[styles.logoutButton, { backgroundColor: colors.error + "10", borderColor: colors.error }]}
          onPress={handleLogout}
        >
          <Text style={[styles.logoutText, { color: colors.error }]}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 20,
  },
  profileSection: {
    flexDirection: "row",
    alignItems: "center",
    padding: 24,
    borderBottomWidth: 1,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  avatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "700",
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  profileRole: {
    fontSize: 14,
    marginBottom: 8,
  },
  verifiedBadge: {
    alignSelf: "flex-start",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verifiedText: {
    fontSize: 10,
    fontWeight: "600",
  },
  modeSwitch: {
    flexDirection: "row",
    alignItems: "center",
    margin: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  modeSwitchContent: {
    flex: 1,
  },
  modeSwitchTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 2,
  },
  modeSwitchSubtitle: {
    fontSize: 12,
  },
  modeSwitchIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  menuSection: {
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 1,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 0.5,
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  menuItemIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 24,
    textAlign: "center",
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  menuItemRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  premiumBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginRight: 8,
  },
  premiumText: {
    fontSize: 10,
    fontWeight: "700",
  },
  menuItemArrow: {
    fontSize: 18,
    fontWeight: "300",
  },
  logoutContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
  },
  logoutButton: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: "center",
  },
  logoutText: {
    fontSize: 16,
    fontWeight: "600",
  },
})
