"use client"

import { useState } from "react"
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

export default function RegisterScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { register } = useUser()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "client" as "client" | "worker",
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleRegister = async () => {
    if (!formData.name || !formData.email || !formData.password) {
      Alert.alert("Error", "Por favor completa todos los campos")
      return
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert("Error", "Las contraseñas no coinciden")
      return
    }

    setIsLoading(true)
    const success = await register({
      name: formData.name,
      email: formData.email,
      role: formData.role,
      password: formData.password,
    })
    setIsLoading(false)

    if (!success) {
      Alert.alert("Error", "Error al crear la cuenta")
    }
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>Crear Cuenta</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>Únete a la comunidad TaskApp</Text>
        </View>

        <View style={styles.form}>
          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
            placeholder="Nombre completo"
            placeholderTextColor={colors.textSecondary}
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
          />

          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
            placeholder="Correo electrónico"
            placeholderTextColor={colors.textSecondary}
            value={formData.email}
            onChangeText={(text) => setFormData({ ...formData, email: text })}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
            placeholder="Contraseña"
            placeholderTextColor={colors.textSecondary}
            value={formData.password}
            onChangeText={(text) => setFormData({ ...formData, password: text })}
            secureTextEntry
          />

          <TextInput
            style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
            placeholder="Confirmar contraseña"
            placeholderTextColor={colors.textSecondary}
            value={formData.confirmPassword}
            onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
            secureTextEntry
          />

          <View style={styles.roleSection}>
            <Text style={[styles.roleTitle, { color: colors.text }]}>¿Cómo quieres usar TaskApp?</Text>

            <TouchableOpacity
              style={[
                styles.roleOption,
                {
                  borderColor: colors.border,
                  backgroundColor: formData.role === "client" ? colors.primary : colors.surface,
                },
              ]}
              onPress={() => setFormData({ ...formData, role: "client" })}
            >
              <Text style={[styles.roleEmoji]}>👨‍💼</Text>
              <Text style={[styles.roleText, { color: formData.role === "client" ? "#FFFFFF" : colors.text }]}>
                Cliente - Contratar servicios
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.roleOption,
                {
                  borderColor: colors.border,
                  backgroundColor: formData.role === "worker" ? colors.primary : colors.surface,
                },
              ]}
              onPress={() => setFormData({ ...formData, role: "worker" })}
            >
              <Text style={[styles.roleEmoji]}>🧑‍🔧</Text>
              <Text style={[styles.roleText, { color: formData.role === "worker" ? "#FFFFFF" : colors.text }]}>
                Trabajador - Ofrecer servicios
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.registerButton, { backgroundColor: colors.primary }]}
            onPress={handleRegister}
            disabled={isLoading}
          >
            <Text style={styles.registerButtonText}>{isLoading ? "Creando cuenta..." : "Crear Cuenta"}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: "center",
    marginBottom: 30,
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    textAlign: "center",
  },
  form: {
    width: "100%",
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 15,
    fontSize: 16,
  },
  roleSection: {
    marginBottom: 20,
  },
  roleTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 15,
  },
  roleOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    borderWidth: 2,
    borderRadius: 10,
    marginBottom: 10,
  },
  roleEmoji: {
    fontSize: 24,
    marginRight: 15,
  },
  roleText: {
    fontSize: 16,
    fontWeight: "500",
  },
  registerButton: {
    height: 50,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 10,
  },
  registerButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
})
