"use client"

import { useState } from "react"
import { View, Text, StyleSheet, TouchableOpacity, Alert, Linking, Dimensions } from "react-native"
import { useTheme } from "../contexts/ThemeContext"

const { width, height } = Dimensions.get("window")

export default function MapScreen({ route, navigation }: any) {
  const { colors } = useTheme()
  const { task } = route.params
  const [mapType, setMapType] = useState<"standard" | "satellite">("standard")

  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(task.location.address)}`
    Linking.openURL(url)
  }

  const openInAppleMaps = () => {
    const url = `http://maps.apple.com/?q=${encodeURIComponent(task.location.address)}`
    Linking.openURL(url)
  }

  const startNavigation = () => {
    Alert.alert("Iniciar Navegación", "¿Con qué aplicación quieres navegar?", [
      { text: "Cancelar", style: "cancel" },
      { text: "Google Maps", onPress: openInGoogleMaps },
      { text: "Apple Maps", onPress: openInAppleMaps },
    ])
  }

  const callClient = () => {
    Alert.alert("Contactar Cliente", "¿Cómo quieres contactar al cliente?", [
      { text: "Cancelar", style: "cancel" },
      { text: "Chat", onPress: () => navigation.navigate("Chat", { taskId: task.id }) },
      { text: "Llamar", onPress: () => Alert.alert("Función", "Llamada telefónica (próximamente)") },
    ])
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Map Container */}
      <View style={[styles.mapContainer, { backgroundColor: colors.surface }]}>
        <View style={styles.mapPlaceholder}>
          <Text style={styles.mapIcon}>🗺️</Text>
          <Text style={[styles.mapText, { color: colors.text }]}>Mapa Interactivo</Text>
          <Text style={[styles.mapSubtext, { color: colors.textSecondary }]}>
            Aquí se mostraría el mapa real con la ubicación exacta
          </Text>

          {/* Simulated Map Pin */}
          <View style={[styles.mapPin, { backgroundColor: colors.error }]}>
            <Text style={styles.pinText}>📍</Text>
          </View>
        </View>

        {/* Map Controls */}
        <View style={styles.mapControls}>
          <TouchableOpacity
            style={[
              styles.mapTypeButton,
              {
                backgroundColor: mapType === "standard" ? colors.primary : colors.background,
                borderColor: colors.border,
              },
            ]}
            onPress={() => setMapType("standard")}
          >
            <Text style={[styles.mapTypeText, { color: mapType === "standard" ? "#FFFFFF" : colors.text }]}>
              Estándar
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.mapTypeButton,
              {
                backgroundColor: mapType === "satellite" ? colors.primary : colors.background,
                borderColor: colors.border,
              },
            ]}
            onPress={() => setMapType("satellite")}
          >
            <Text style={[styles.mapTypeText, { color: mapType === "satellite" ? "#FFFFFF" : colors.text }]}>
              Satélite
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Location Info */}
      <View style={[styles.locationInfo, { backgroundColor: colors.surface }]}>
        <View style={styles.locationHeader}>
          <Text style={[styles.locationTitle, { color: colors.text }]}>{task.title}</Text>
          <View style={[styles.distanceBadge, { backgroundColor: colors.primary }]}>
            <Text style={styles.distanceText}>2.3 km</Text>
          </View>
        </View>

        <View style={styles.addressContainer}>
          <Text style={styles.addressIcon}>📍</Text>
          <Text style={[styles.addressText, { color: colors.textSecondary }]}>{task.location.address}</Text>
        </View>

        <View style={styles.estimatedTime}>
          <Text style={styles.timeIcon}>🚗</Text>
          <Text style={[styles.timeText, { color: colors.text }]}>8 min en auto • 15 min caminando</Text>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.background, borderColor: colors.border }]}
          onPress={callClient}
        >
          <Text style={styles.actionIcon}>💬</Text>
          <Text style={[styles.actionText, { color: colors.text }]}>Contactar</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.background, borderColor: colors.border }]}
          onPress={() => Alert.alert("Función", "Compartir ubicación (próximamente)")}
        >
          <Text style={styles.actionIcon}>📤</Text>
          <Text style={[styles.actionText, { color: colors.text }]}>Compartir</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.background, borderColor: colors.border }]}
          onPress={() => Alert.alert("Función", "Guardar ubicación (próximamente)")}
        >
          <Text style={styles.actionIcon}>⭐</Text>
          <Text style={[styles.actionText, { color: colors.text }]}>Guardar</Text>
        </TouchableOpacity>
      </View>

      {/* Navigation Button */}
      <View style={styles.navigationContainer}>
        <TouchableOpacity
          style={[styles.navigationButton, { backgroundColor: colors.primary }]}
          onPress={startNavigation}
        >
          <Text style={styles.navigationIcon}>🧭</Text>
          <Text style={styles.navigationText}>Iniciar Navegación</Text>
        </TouchableOpacity>
      </View>

      {/* External Map Options */}
      <View style={[styles.externalOptions, { backgroundColor: colors.surface }]}>
        <Text style={[styles.optionsTitle, { color: colors.text }]}>Abrir en otra app</Text>
        <View style={styles.optionsButtons}>
          <TouchableOpacity
            style={[styles.externalButton, { backgroundColor: colors.background, borderColor: colors.border }]}
            onPress={openInGoogleMaps}
          >
            <Text style={styles.externalIcon}>🌍</Text>
            <Text style={[styles.externalText, { color: colors.text }]}>Google Maps</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.externalButton, { backgroundColor: colors.background, borderColor: colors.border }]}
            onPress={openInAppleMaps}
          >
            <Text style={styles.externalIcon}>🍎</Text>
            <Text style={[styles.externalText, { color: colors.text }]}>Apple Maps</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mapContainer: {
    height: height * 0.5,
    margin: 20,
    borderRadius: 15,
    overflow: "hidden",
    position: "relative",
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  mapIcon: {
    fontSize: 60,
    marginBottom: 15,
  },
  mapText: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  mapSubtext: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  mapPin: {
    position: "absolute",
    top: "50%",
    left: "50%",
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    transform: [{ translateX: -20 }, { translateY: -20 }],
  },
  pinText: {
    fontSize: 20,
  },
  mapControls: {
    position: "absolute",
    top: 15,
    right: 15,
    flexDirection: "row",
  },
  mapTypeButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginLeft: 10,
  },
  mapTypeText: {
    fontSize: 12,
    fontWeight: "600",
  },
  locationInfo: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  locationHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 15,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: "bold",
    flex: 1,
    marginRight: 15,
  },
  distanceBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
  },
  distanceText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  addressContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 10,
  },
  addressIcon: {
    fontSize: 16,
    marginRight: 10,
    marginTop: 2,
  },
  addressText: {
    fontSize: 16,
    flex: 1,
    lineHeight: 22,
  },
  estimatedTime: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeIcon: {
    fontSize: 16,
    marginRight: 10,
  },
  timeText: {
    fontSize: 14,
  },
  quickActions: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  actionButton: {
    alignItems: "center",
    padding: 15,
    borderRadius: 15,
    borderWidth: 1,
    minWidth: 80,
  },
  actionIcon: {
    fontSize: 20,
    marginBottom: 5,
  },
  actionText: {
    fontSize: 12,
    fontWeight: "600",
  },
  navigationContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  navigationButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 18,
    borderRadius: 15,
  },
  navigationIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  navigationText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  externalOptions: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 15,
  },
  optionsButtons: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  externalButton: {
    alignItems: "center",
    padding: 15,
    borderRadius: 15,
    borderWidth: 1,
    minWidth: 120,
  },
  externalIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  externalText: {
    fontSize: 14,
    fontWeight: "600",
  },
})
