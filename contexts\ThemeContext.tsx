"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

type Theme = "light" | "dark"

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  colors: {
    primary: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    success: string
    warning: string
    error: string
    accent: string
  }
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

const lightColors = {
  primary: "#4A90E2",
  secondary: "#6C7B7F",
  background: "#FFFFFF",
  surface: "#F8F9FA",
  text: "#2C3E50",
  textSecondary: "#6C757D",
  border: "#E9ECEF",
  success: "#28A745",
  warning: "#FFC107",
  error: "#DC3545",
  accent: "#17A2B8",
}

const darkColors = {
  primary: "#5BA0F2",
  secondary: "#8A9BA8",
  background: "#1A1D23",
  surface: "#2D3748",
  text: "#F7FAFC",
  textSecondary: "#A0AEC0",
  border: "#4A5568",
  success: "#48BB78",
  warning: "#ED8936",
  error: "#F56565",
  accent: "#4FD1C7",
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>("light")

  useEffect(() => {
    loadTheme()
  }, [])

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem("theme")
      if (savedTheme) {
        setTheme(savedTheme as Theme)
      }
    } catch (error) {
      console.error("Error loading theme:", error)
    }
  }

  const toggleTheme = async () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    try {
      await AsyncStorage.setItem("theme", newTheme)
    } catch (error) {
      console.error("Error saving theme:", error)
    }
  }

  const colors = theme === "light" ? lightColors : darkColors

  return <ThemeContext.Provider value={{ theme, toggleTheme, colors }}>{children}</ThemeContext.Provider>
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}
