"use client"

import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView, Alert } from "react-native"
import { useTheme } from "../contexts/ThemeContext"

export default function CertificationsScreen({ navigation }: any) {
  const { colors } = useTheme()

  const mockCertifications = [
    {
      id: "1",
      title: "Certificación en Plomería",
      issuer: "Instituto Técnico Nacional",
      status: "verified",
      date: "2023-06-15",
      expiryDate: "2025-06-15",
    },
    {
      id: "2",
      title: "Certificación en Electricidad",
      issuer: "Colegio de Electricistas",
      status: "pending",
      date: "2024-01-10",
      expiryDate: "2026-01-10",
    },
    {
      id: "3",
      title: "Certificación en Seguridad Laboral",
      issuer: "Ministerio de Trabajo",
      status: "expired",
      date: "2022-03-20",
      expiryDate: "2024-03-20",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "verified":
        return colors.success
      case "pending":
        return colors.warning
      case "expired":
        return colors.error
      default:
        return colors.textSecondary
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "verified":
        return "Verificada"
      case "pending":
        return "Pendiente"
      case "expired":
        return "Expirada"
      default:
        return "Desconocido"
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backIcon, { color: colors.text }]}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Certificaciones</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={() => Alert.alert("Función Premium", "Agregar certificación - Próximamente")}
        >
          <Text style={styles.addIcon}>+</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {mockCertifications.map((cert) => (
          <TouchableOpacity
            key={cert.id}
            style={[styles.certCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
            onPress={() => Alert.alert("Ver Certificación", "Detalles de la certificación - Función Premium")}
          >
            <View style={styles.certHeader}>
              <View style={styles.certTitleContainer}>
                <Text style={[styles.certTitle, { color: colors.text }]}>{cert.title}</Text>
                <Text style={[styles.certIssuer, { color: colors.textSecondary }]}>{cert.issuer}</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(cert.status) + "15" }]}>
                <Text style={[styles.statusText, { color: getStatusColor(cert.status) }]}>
                  {getStatusText(cert.status)}
                </Text>
              </View>
            </View>

            <View style={styles.certFooter}>
              <View style={styles.certDates}>
                <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>Emitida: {cert.date}</Text>
                <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>Expira: {cert.expiryDate}</Text>
              </View>
              <View style={styles.certActions}>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: colors.primary + "15" }]}
                  onPress={() => Alert.alert("Ver", "Ver certificado completo")}
                >
                  <Text style={[styles.actionButtonText, { color: colors.primary }]}>Ver</Text>
                </TouchableOpacity>
                {cert.status === "expired" && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: colors.warning + "15" }]}
                    onPress={() => Alert.alert("Renovar", "Renovar certificación")}
                  >
                    <Text style={[styles.actionButtonText, { color: colors.warning }]}>Renovar</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  backIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  addIcon: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    padding: 20,
  },
  certCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  certHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  certTitleContainer: {
    flex: 1,
    marginRight: 16,
  },
  certTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  certIssuer: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  certFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  certDates: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  certActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: "600",
  },
})
