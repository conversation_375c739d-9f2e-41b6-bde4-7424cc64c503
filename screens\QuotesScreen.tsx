"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView, Alert } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

export default function QuotesScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const [activeTab, setActiveTab] = useState<"received" | "sent" | "drafts">("received")

  const mockQuotes = [
    {
      id: "1",
      title: "Reparación de plomería",
      client: "<PERSON>",
      amount: 150,
      status: "pending",
      date: "2024-01-15",
      description: "Reparación de grifo y revisión de tuberías",
    },
    {
      id: "2",
      title: "Limpieza profunda",
      client: "Carlos Mendoza",
      amount: 200,
      status: "accepted",
      date: "2024-01-14",
      description: "Limpieza completa de apartamento 2 habitaciones",
    },
    {
      id: "3",
      title: "Instalación eléctrica",
      client: "<PERSON>",
      amount: 300,
      status: "rejected",
      date: "2024-01-13",
      description: "Instalación de nuevos puntos de luz",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return colors.warning
      case "accepted":
        return colors.success
      case "rejected":
        return colors.error
      default:
        return colors.textSecondary
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pendiente"
      case "accepted":
        return "Aceptada"
      case "rejected":
        return "Rechazada"
      default:
        return "Desconocido"
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backIcon, { color: colors.text }]}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Cotizaciones</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate("CreateQuote")}
        >
          <Text style={styles.addIcon}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={[styles.tabsContainer, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === "received" ? colors.primary : "transparent" }]}
          onPress={() => setActiveTab("received")}
        >
          <Text style={[styles.tabText, { color: activeTab === "received" ? "#FFFFFF" : colors.text }]}>Recibidas</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === "sent" ? colors.primary : "transparent" }]}
          onPress={() => setActiveTab("sent")}
        >
          <Text style={[styles.tabText, { color: activeTab === "sent" ? "#FFFFFF" : colors.text }]}>Enviadas</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, { backgroundColor: activeTab === "drafts" ? colors.primary : "transparent" }]}
          onPress={() => setActiveTab("drafts")}
        >
          <Text style={[styles.tabText, { color: activeTab === "drafts" ? "#FFFFFF" : colors.text }]}>Borradores</Text>
        </TouchableOpacity>
      </View>

      {/* Quotes List */}
      <ScrollView style={styles.quotesList} showsVerticalScrollIndicator={false}>
        {mockQuotes.map((quote) => (
          <TouchableOpacity
            key={quote.id}
            style={[styles.quoteCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
            onPress={() => navigation.navigate("QuoteDetail", { quoteId: quote.id })}
          >
            <View style={styles.quoteHeader}>
              <View style={styles.quoteTitleContainer}>
                <Text style={[styles.quoteTitle, { color: colors.text }]}>{quote.title}</Text>
                <Text style={[styles.quoteClient, { color: colors.textSecondary }]}>{quote.client}</Text>
              </View>
              <View style={styles.quoteAmount}>
                <Text style={[styles.amountText, { color: colors.success }]}>${quote.amount}</Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(quote.status) + "15" }]}>
                  <Text style={[styles.statusText, { color: getStatusColor(quote.status) }]}>
                    {getStatusText(quote.status)}
                  </Text>
                </View>
              </View>
            </View>

            <Text style={[styles.quoteDescription, { color: colors.textSecondary }]} numberOfLines={2}>
              {quote.description}
            </Text>

            <View style={styles.quoteFooter}>
              <Text style={[styles.quoteDate, { color: colors.textSecondary }]}>{quote.date}</Text>
              <View style={styles.quoteActions}>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: colors.primary + "15" }]}
                  onPress={() => Alert.alert("Ver", "Ver detalles de cotización")}
                >
                  <Text style={[styles.actionButtonText, { color: colors.primary }]}>Ver</Text>
                </TouchableOpacity>
                {quote.status === "pending" && (
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: colors.success + "15" }]}
                    onPress={() => Alert.alert("Aceptar", "Cotización aceptada")}
                  >
                    <Text style={[styles.actionButtonText, { color: colors.success }]}>Aceptar</Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  backIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  addIcon: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  tabsContainer: {
    flexDirection: "row",
    padding: 20,
    paddingTop: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 4,
    alignItems: "center",
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
  },
  quotesList: {
    flex: 1,
    padding: 20,
  },
  quoteCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  quoteHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  quoteTitleContainer: {
    flex: 1,
    marginRight: 16,
  },
  quoteTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  quoteClient: {
    fontSize: 14,
  },
  quoteAmount: {
    alignItems: "flex-end",
  },
  amountText: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  quoteDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  quoteFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  quoteDate: {
    fontSize: 12,
  },
  quoteActions: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: "600",
  },
})
