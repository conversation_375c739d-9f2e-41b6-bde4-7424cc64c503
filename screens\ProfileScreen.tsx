"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Image, Alert, Switch } from "react-native"
import * as ImagePicker from "expo-image-picker"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

export default function ProfileScreen() {
  const { colors, theme, toggleTheme } = useTheme()
  const { user, updateProfile, logout, switchMode } = useUser()
  const [isEditing, setIsEditing] = useState(false)
  const [editData, setEditData] = useState({
    name: user?.name || "",
    bio: user?.bio || "",
  })

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    })

    if (!result.canceled) {
      await updateProfile({ avatar: result.assets[0].uri })
    }
  }

  const saveProfile = async () => {
    await updateProfile(editData)
    setIsEditing(false)
    Alert.alert("Éxito", "Perfil actualizado correctamente")
  }

  const handleLogout = () => {
    Alert.alert("Cerrar Sesión", "¿Estás seguro que quieres cerrar sesión?", [
      { text: "Cancelar", style: "cancel" },
      { text: "Cerrar Sesión", onPress: logout, style: "destructive" },
    ])
  }

  const handleModeSwitch = async () => {
    const newMode = user?.currentMode === "client" ? "worker" : "client"
    await switchMode(newMode)
    Alert.alert("Modo Cambiado", `Ahora estás en modo ${newMode === "client" ? "Cliente" : "Trabajador"}`)
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Professional Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={styles.profileSection}>
          <TouchableOpacity onPress={pickImage} style={styles.avatarContainer}>
            {user?.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.avatar} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primary }]}>
                <Text style={styles.avatarText}>{user?.name?.charAt(0).toUpperCase()}</Text>
              </View>
            )}
            <View style={[styles.editBadge, { backgroundColor: colors.background, borderColor: colors.border }]}>
              <Text style={[styles.editBadgeText, { color: colors.primary }]}>✏️</Text>
            </View>
          </TouchableOpacity>

          <View style={styles.profileInfo}>
            <Text style={[styles.profileName, { color: colors.text }]}>{user?.name}</Text>
            <Text style={[styles.profileEmail, { color: colors.textSecondary }]}>{user?.email}</Text>

            {user?.isVerified && (
              <View style={[styles.verifiedBadge, { backgroundColor: colors.success + "15" }]}>
                <Text style={[styles.verifiedIcon, { color: colors.success }]}>✓</Text>
                <Text style={[styles.verifiedText, { color: colors.success }]}>Verificado</Text>
              </View>
            )}
          </View>
        </View>

        {user?.rating && (
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.primary }]}>{user.rating}</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Calificación</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.success }]}>{user.completedTasks}</Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Completados</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.accent }]}>
                {user.joinedDate ? new Date().getFullYear() - user.joinedDate.getFullYear() : 0}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Años</Text>
            </View>
          </View>
        )}
      </View>

      {/* Mode Switch */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Modo de Usuario</Text>
          <View style={[styles.currentModeBadge, { backgroundColor: colors.primary + "15" }]}>
            <Text style={[styles.currentModeText, { color: colors.primary }]}>
              {user?.currentMode === "client" ? "Cliente" : "Trabajador"}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.modeSwitchButton, { backgroundColor: colors.background, borderColor: colors.border }]}
          onPress={handleModeSwitch}
        >
          <View style={styles.modeSwitchContent}>
            <Text style={[styles.modeSwitchTitle, { color: colors.text }]}>
              Cambiar a modo {user?.currentMode === "client" ? "Trabajador" : "Cliente"}
            </Text>
            <Text style={[styles.modeSwitchSubtitle, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Ofrece tus servicios profesionales" : "Contrata servicios de calidad"}
            </Text>
          </View>
          <Text style={[styles.modeSwitchIcon, { color: colors.primary }]}>→</Text>
        </TouchableOpacity>
      </View>

      {/* Personal Information */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Información Personal</Text>
          <TouchableOpacity
            onPress={() => setIsEditing(!isEditing)}
            style={[styles.editButton, { backgroundColor: isEditing ? colors.error : colors.primary }]}
          >
            <Text style={styles.editButtonText}>{isEditing ? "Cancelar" : "Editar"}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoGroup}>
          <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Nombre Completo</Text>
          {isEditing ? (
            <TextInput
              style={[
                styles.infoInput,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              value={editData.name}
              onChangeText={(text) => setEditData({ ...editData, name: text })}
            />
          ) : (
            <Text style={[styles.infoValue, { color: colors.text }]}>{user?.name}</Text>
          )}
        </View>

        <View style={styles.infoGroup}>
          <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Correo Electrónico</Text>
          <Text style={[styles.infoValue, { color: colors.text }]}>{user?.email}</Text>
        </View>

        <View style={styles.infoGroup}>
          <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Biografía Profesional</Text>
          {isEditing ? (
            <TextInput
              style={[
                styles.bioInput,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              value={editData.bio}
              onChangeText={(text) => setEditData({ ...editData, bio: text })}
              multiline
              numberOfLines={4}
              placeholder="Describe tu experiencia profesional..."
              placeholderTextColor={colors.textSecondary}
            />
          ) : (
            <Text style={[styles.infoValue, { color: colors.text }]}>{user?.bio || "Sin biografía profesional"}</Text>
          )}
        </View>

        {isEditing && (
          <TouchableOpacity style={[styles.saveButton, { backgroundColor: colors.success }]} onPress={saveProfile}>
            <Text style={styles.saveButtonText}>Guardar Cambios</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Settings */}
      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Configuración</Text>

        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <Text style={[styles.settingIcon, { color: colors.primary }]}>🌙</Text>
            <Text style={[styles.settingLabel, { color: colors.text }]}>Modo Oscuro</Text>
          </View>
          <Switch
            value={theme === "dark"}
            onValueChange={toggleTheme}
            trackColor={{ false: colors.border, true: colors.primary }}
            thumbColor={theme === "dark" ? "#FFFFFF" : colors.textSecondary}
          />
        </View>

        <TouchableOpacity style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <Text style={[styles.settingIcon, { color: colors.accent }]}>🔔</Text>
            <Text style={[styles.settingLabel, { color: colors.text }]}>Notificaciones</Text>
          </View>
          <Text style={[styles.settingArrow, { color: colors.textSecondary }]}>→</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <Text style={[styles.settingIcon, { color: colors.warning }]}>🔒</Text>
            <Text style={[styles.settingLabel, { color: colors.text }]}>Privacidad y Seguridad</Text>
          </View>
          <Text style={[styles.settingArrow, { color: colors.textSecondary }]}>→</Text>
        </TouchableOpacity>
      </View>

      {/* Logout */}
      <View style={styles.logoutContainer}>
        <TouchableOpacity style={[styles.logoutButton, { backgroundColor: colors.error }]} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 24,
    paddingTop: 20,
  },
  profileSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
  },
  avatarContainer: {
    position: "relative",
    marginRight: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#FFFFFF",
    fontSize: 32,
    fontWeight: "700",
  },
  editBadge: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 2,
    justifyContent: "center",
    alignItems: "center",
  },
  editBadgeText: {
    fontSize: 12,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 16,
    marginBottom: 12,
  },
  verifiedBadge: {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-start",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  verifiedIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  verifiedText: {
    fontSize: 12,
    fontWeight: "600",
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: "500",
  },
  section: {
    marginHorizontal: 24,
    marginBottom: 20,
    padding: 24,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  currentModeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  currentModeText: {
    fontSize: 12,
    fontWeight: "600",
  },
  modeSwitchButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
  },
  modeSwitchContent: {
    flex: 1,
  },
  modeSwitchTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  modeSwitchSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  modeSwitchIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  editButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  editButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  infoGroup: {
    marginBottom: 20,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  infoValue: {
    fontSize: 16,
    lineHeight: 22,
  },
  infoInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  bioInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
  },
  saveButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 12,
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingIcon: {
    fontSize: 20,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  settingArrow: {
    fontSize: 16,
  },
  logoutContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  logoutButton: {
    padding: 18,
    borderRadius: 12,
    alignItems: "center",
  },
  logoutButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
})
