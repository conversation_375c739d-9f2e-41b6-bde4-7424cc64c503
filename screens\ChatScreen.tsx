"use client"

import { useState, useRef, useEffect } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

interface ChatMessage {
  id: string
  text: string
  isUser: boolean
  timestamp: Date
}

export default function ChatScreen({ route }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const { workerId, workerName } = route.params || {}
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "1",
      text: `¡Hola! Soy ${workerName || "el trabajador"}. ¿En qué puedo ayudarte hoy?`,
      isUser: false,
      timestamp: new Date(),
    },
  ])
  const [inputText, setInputText] = useState("")
  const scrollViewRef = useRef<ScrollView>(null)

  const sendMessage = () => {
    if (!inputText.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputText("")

    // Simulate response
    setTimeout(() => {
      const responses = [
        "Perfecto, puedo ayudarte con eso. ¿Cuándo te gustaría que lo haga?",
        "Entiendo. ¿Podrías darme más detalles sobre lo que necesitas?",
        "Claro, tengo experiencia en ese tipo de trabajo. ¿Cuál es tu presupuesto?",
        "Excelente. ¿Tienes alguna preferencia de horario?",
        "Sin problema. ¿La dirección está en la zona que mencionaste?",
      ]

      const response: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: responses[Math.floor(Math.random() * responses.length)],
        isUser: false,
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, response])
    }, 1000)
  }

  const showWarning = () => {
    Alert.alert(
      "Seguridad",
      "Recuerda:\n• No compartas información personal como dirección exacta hasta confirmar el servicio\n• Mantén toda la comunicación dentro de la app\n• Reporta cualquier comportamiento inapropiado",
      [{ text: "Entendido" }],
    )
  }

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true })
  }, [messages])

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <View>
          <Text style={[styles.headerTitle, { color: colors.text }]}>{workerName || "Chat"}</Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>En línea</Text>
        </View>
        <TouchableOpacity style={[styles.warningButton, { backgroundColor: colors.warning }]} onPress={showWarning}>
          <Text style={styles.warningButtonText}>⚠️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView ref={scrollViewRef} style={styles.messagesContainer} contentContainerStyle={styles.messagesContent}>
        {messages.map((message) => (
          <View
            key={message.id}
            style={[styles.messageContainer, message.isUser ? styles.userMessage : styles.otherMessage]}
          >
            <View
              style={[
                styles.messageBubble,
                {
                  backgroundColor: message.isUser ? colors.primary : colors.surface,
                },
              ]}
            >
              <Text style={[styles.messageText, { color: message.isUser ? "#FFFFFF" : colors.text }]}>
                {message.text}
              </Text>
            </View>
            <Text style={[styles.messageTime, { color: colors.textSecondary }]}>
              {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
            </Text>
          </View>
        ))}
      </ScrollView>

      <View style={[styles.inputContainer, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
        <TextInput
          style={[
            styles.textInput,
            { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
          ]}
          placeholder="Escribe un mensaje..."
          placeholderTextColor={colors.textSecondary}
          value={inputText}
          onChangeText={setInputText}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[styles.sendButton, { backgroundColor: colors.primary }]}
          onPress={sendMessage}
          disabled={!inputText.trim()}
        >
          <Text style={styles.sendButtonText}>Enviar</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 10,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  headerSubtitle: {
    fontSize: 14,
  },
  warningButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  warningButtonText: {
    fontSize: 16,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 20,
  },
  messageContainer: {
    marginBottom: 15,
  },
  userMessage: {
    alignItems: "flex-end",
  },
  otherMessage: {
    alignItems: "flex-start",
  },
  messageBubble: {
    maxWidth: "80%",
    padding: 15,
    borderRadius: 20,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 5,
    marginHorizontal: 5,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    padding: 20,
    borderTopWidth: 1,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 10,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  sendButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
})
