"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"

export default function ClientHomeScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { user } = useUser()
  const [taskDescription, setTaskDescription] = useState("")

  const createTask = () => {
    if (!taskDescription.trim()) {
      Alert.alert("Error", "Por favor describe la tarea que necesitas")
      return
    }

    Alert.alert("Tarea Creada", "Tu tarea ha sido publicada. Los trabajadores cercanos serán notificados.", [
      { text: "OK", onPress: () => setTaskDescription("") },
    ])
  }

  const mockWorkers = [
    { id: "1", name: "<PERSON>", rating: 4.9, specialty: "P<PERSON>mería", isLive: true },
    { id: "2", name: "<PERSON>", rating: 4.8, specialty: "<PERSON><PERSON><PERSON>", isLive: false },
    { id: "3", name: "<PERSON>", rating: 4.7, specialty: "Electricidad", isLive: true },
  ]

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.greeting, { color: colors.text }]}>¡Hola, {user?.name}! 👋</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>¿Qué tarea necesitas hoy?</Text>
      </View>

      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate("TuAmigo")}
        >
          <Text style={styles.actionEmoji}>🤗</Text>
          <Text style={styles.actionText}>TuAmigo</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.success }]}
          onPress={() => navigation.navigate("Goals")}
        >
          <Text style={styles.actionEmoji}>🎯</Text>
          <Text style={styles.actionText}>Mis Metas</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.warning }]}
          onPress={() => navigation.navigate("Profile")}
        >
          <Text style={styles.actionEmoji}>👤</Text>
          <Text style={styles.actionText}>Perfil</Text>
        </TouchableOpacity>
      </View>

      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Crear Nueva Tarea</Text>
        <TextInput
          style={[
            styles.taskInput,
            { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
          ]}
          placeholder="Describe la tarea que necesitas (ej: Reparar grifo de la cocina)"
          placeholderTextColor={colors.textSecondary}
          value={taskDescription}
          onChangeText={setTaskDescription}
          multiline
          numberOfLines={3}
        />
        <TouchableOpacity style={[styles.createButton, { backgroundColor: colors.primary }]} onPress={createTask}>
          <Text style={styles.createButtonText}>Publicar Tarea</Text>
        </TouchableOpacity>
      </View>

      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Trabajadores Disponibles</Text>
        {mockWorkers.map((worker) => (
          <TouchableOpacity
            key={worker.id}
            style={[styles.workerCard, { backgroundColor: colors.background, borderColor: colors.border }]}
            onPress={() => navigation.navigate("Chat", { workerId: worker.id, workerName: worker.name })}
          >
            <View style={styles.workerInfo}>
              <Text style={[styles.workerName, { color: colors.text }]}>{worker.name}</Text>
              <Text style={[styles.workerSpecialty, { color: colors.textSecondary }]}>{worker.specialty}</Text>
              <Text style={[styles.workerRating, { color: colors.warning }]}>⭐ {worker.rating}</Text>
            </View>
            {worker.isLive && (
              <TouchableOpacity
                style={[styles.liveButton, { backgroundColor: colors.error }]}
                onPress={() => navigation.navigate("Live", { workerId: worker.id })}
              >
                <Text style={styles.liveButtonText}>🔴 EN VIVO</Text>
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        ))}
      </View>

      <View style={[styles.section, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Estadísticas del Mes</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>5</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Tareas Completadas</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.success }]}>$250</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Gastado</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.warning }]}>4.9</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Calificación</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  greeting: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
  },
  quickActions: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  actionButton: {
    alignItems: "center",
    padding: 15,
    borderRadius: 15,
    minWidth: 80,
  },
  actionEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  actionText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  section: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  taskInput: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    minHeight: 80,
    textAlignVertical: "top",
  },
  createButton: {
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
  },
  createButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  workerCard: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    marginBottom: 10,
  },
  workerInfo: {
    flex: 1,
  },
  workerName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  workerSpecialty: {
    fontSize: 14,
    marginBottom: 2,
  },
  workerRating: {
    fontSize: 14,
  },
  liveButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  liveButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    textAlign: "center",
  },
})
