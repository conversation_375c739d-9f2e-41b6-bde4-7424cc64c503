"use client"

import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView, Alert } from "react-native"
import { useTheme } from "../contexts/ThemeContext"

export default function SupportScreen({ navigation }: any) {
  const { colors } = useTheme()

  const faqItems = [
    {
      question: "¿Cómo puedo verificar mi cuenta?",
      answer:
        "Para verificar tu cuenta, ve a Configuración > Verificación de Identidad y sube los documentos requeridos.",
    },
    {
      question: "¿Cuándo recibo mis pagos?",
      answer: "Los pagos se procesan automáticamente 24-48 horas después de completar un proyecto.",
    },
    {
      question: "¿Cómo puedo cambiar mi método de pago?",
      answer: "Ve a Configuración > Métodos de Pago para agregar o cambiar tus opciones de pago.",
    },
    {
      question: "¿Qué hago si tengo un problema con un cliente?",
      answer:
        "Contacta a nuestro equipo de soporte inmediatamente. Estamos aquí para ayudarte a resolver cualquier conflicto.",
    },
  ]

  const supportOptions = [
    {
      title: "Chat en Vivo",
      description: "Habla con nuestro equipo de soporte",
      icon: "💬",
      action: () => Alert.alert("Chat", "Iniciando chat en vivo..."),
    },
    {
      title: "Enviar Email",
      description: "<EMAIL>",
      icon: "📧",
      action: () => Alert.alert("Email", "Abriendo cliente de email..."),
    },
    {
      title: "Llamar Soporte",
      description: "+1 (555) 123-4567",
      icon: "📞",
      action: () => Alert.alert("Llamada", "Iniciando llamada..."),
    },
    {
      title: "Centro de Ayuda",
      description: "Guías y tutoriales completos",
      icon: "📚",
      action: () => Alert.alert("Ayuda", "Abriendo centro de ayuda..."),
    },
  ]

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.backIcon, { color: colors.text }]}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Soporte</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Support Options */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>¿Necesitas ayuda?</Text>
          <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
            Elige la opción que mejor se adapte a tu necesidad
          </Text>

          {supportOptions.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.supportOption, { backgroundColor: colors.background, borderColor: colors.border }]}
              onPress={option.action}
            >
              <View style={styles.supportOptionLeft}>
                <Text style={styles.supportIcon}>{option.icon}</Text>
                <View style={styles.supportOptionContent}>
                  <Text style={[styles.supportOptionTitle, { color: colors.text }]}>{option.title}</Text>
                  <Text style={[styles.supportOptionDescription, { color: colors.textSecondary }]}>
                    {option.description}
                  </Text>
                </View>
              </View>
              <Text style={[styles.supportOptionArrow, { color: colors.textSecondary }]}>›</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* FAQ */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Preguntas Frecuentes</Text>

          {faqItems.map((item, index) => (
            <View key={index} style={[styles.faqItem, { borderBottomColor: colors.border }]}>
              <Text style={[styles.faqQuestion, { color: colors.text }]}>{item.question}</Text>
              <Text style={[styles.faqAnswer, { color: colors.textSecondary }]}>{item.answer}</Text>
            </View>
          ))}
        </View>

        {/* Emergency Contact */}
        <View style={[styles.section, { backgroundColor: colors.error + "10", borderColor: colors.error }]}>
          <Text style={[styles.emergencyTitle, { color: colors.error }]}>🚨 Contacto de Emergencia</Text>
          <Text style={[styles.emergencyText, { color: colors.text }]}>
            Si tienes una emergencia relacionada con seguridad o necesitas ayuda inmediata, contacta a nuestro equipo de
            emergencia 24/7.
          </Text>
          <TouchableOpacity
            style={[styles.emergencyButton, { backgroundColor: colors.error }]}
            onPress={() => Alert.alert("Emergencia", "Contactando equipo de emergencia...")}
          >
            <Text style={styles.emergencyButtonText}>Contactar Emergencia</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    paddingTop: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  backIcon: {
    fontSize: 20,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 20,
    lineHeight: 20,
  },
  supportOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  supportOptionLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  supportIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  supportOptionContent: {
    flex: 1,
  },
  supportOptionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  supportOptionDescription: {
    fontSize: 14,
  },
  supportOptionArrow: {
    fontSize: 18,
    fontWeight: "300",
  },
  faqItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  faqAnswer: {
    fontSize: 14,
    lineHeight: 20,
  },
  emergencyTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  emergencyText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  emergencyButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  emergencyButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
})
