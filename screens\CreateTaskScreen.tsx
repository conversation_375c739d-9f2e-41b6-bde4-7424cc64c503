"use client"

import { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useTasks } from "../contexts/TaskContext"

export default function CreateTaskScreen({ navigation }: any) {
  const { colors } = useTheme()
  const { createTask } = useTasks()
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    category: "",
    address: "",
    minBudget: "",
    maxBudget: "",
    urgency: "medium" as "low" | "medium" | "high",
    estimatedDuration: "",
    requirements: "",
  })

  const categories = [
    "Limpieza",
    "Plomería",
    "Electricidad",
    "Jardinería",
    "Pintura",
    "Mudanza",
    "Carpintería",
    "Tecnología",
  ]

  const handleSubmit = async () => {
    if (!formData.title || !formData.description || !formData.category || !formData.address) {
      Alert.alert("Error", "Por favor completa todos los campos obligatorios")
      return
    }

    if (!formData.minBudget || !formData.maxBudget) {
      Alert.alert("Error", "Por favor especifica el presupuesto")
      return
    }

    try {
      await createTask({
        title: formData.title,
        description: formData.description,
        category: formData.category,
        location: {
          address: formData.address,
          coordinates: {
            latitude: -34.6037 + (Math.random() - 0.5) * 0.1,
            longitude: -58.3816 + (Math.random() - 0.5) * 0.1,
          },
        },
        budget: {
          min: Number.parseFloat(formData.minBudget),
          max: Number.parseFloat(formData.maxBudget),
        },
        urgency: formData.urgency,
        estimatedDuration: Number.parseFloat(formData.estimatedDuration) || 2,
        requirements: formData.requirements
          .split(",")
          .map((req) => req.trim())
          .filter(Boolean),
        clientId: "current_user",
      })

      Alert.alert("¡Éxito!", "Tu tarea ha sido publicada exitosamente", [
        { text: "OK", onPress: () => navigation.goBack() },
      ])
    } catch (error) {
      Alert.alert("Error", "No se pudo crear la tarea")
    }
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.scrollContent}>
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Información Básica</Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Título de la tarea *</Text>
            <TextInput
              style={[
                styles.input,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="Ej: Reparar grifo de cocina"
              placeholderTextColor={colors.textSecondary}
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Descripción detallada *</Text>
            <TextInput
              style={[
                styles.textArea,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="Describe exactamente qué necesitas que se haga..."
              placeholderTextColor={colors.textSecondary}
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Categoría *</Text>
            <View style={styles.categoryGrid}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    {
                      backgroundColor: formData.category === category ? colors.primary : colors.background,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={() => setFormData({ ...formData, category })}
                >
                  <Text
                    style={[
                      styles.categoryButtonText,
                      { color: formData.category === category ? "#FFFFFF" : colors.text },
                    ]}
                  >
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Ubicación</Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Dirección *</Text>
            <TextInput
              style={[
                styles.input,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="Ej: Av. Corrientes 1234, Buenos Aires"
              placeholderTextColor={colors.textSecondary}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
            />
          </View>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Presupuesto y Tiempo</Text>

          <View style={styles.budgetRow}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
              <Text style={[styles.label, { color: colors.text }]}>Presupuesto mínimo *</Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="50"
                placeholderTextColor={colors.textSecondary}
                value={formData.minBudget}
                onChangeText={(text) => setFormData({ ...formData, minBudget: text })}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
              <Text style={[styles.label, { color: colors.text }]}>Presupuesto máximo *</Text>
              <TextInput
                style={[
                  styles.input,
                  { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
                ]}
                placeholder="100"
                placeholderTextColor={colors.textSecondary}
                value={formData.maxBudget}
                onChangeText={(text) => setFormData({ ...formData, maxBudget: text })}
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Duración estimada (horas)</Text>
            <TextInput
              style={[
                styles.input,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="2"
              placeholderTextColor={colors.textSecondary}
              value={formData.estimatedDuration}
              onChangeText={(text) => setFormData({ ...formData, estimatedDuration: text })}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Urgencia</Text>
            <View style={styles.urgencyButtons}>
              {[
                { key: "low", label: "Flexible", color: "#34C759" },
                { key: "medium", label: "Moderado", color: "#FF9500" },
                { key: "high", label: "Urgente", color: "#FF3B30" },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.urgencyButton,
                    {
                      backgroundColor: formData.urgency === option.key ? option.color : colors.background,
                      borderColor: option.color,
                    },
                  ]}
                  onPress={() => setFormData({ ...formData, urgency: option.key as any })}
                >
                  <Text
                    style={[
                      styles.urgencyButtonText,
                      { color: formData.urgency === option.key ? "#FFFFFF" : option.color },
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Requisitos Adicionales</Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>Requisitos (separados por comas)</Text>
            <TextInput
              style={[
                styles.textArea,
                { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
              ]}
              placeholder="Ej: Herramientas propias, Experiencia mínima 2 años, Referencias"
              placeholderTextColor={colors.textSecondary}
              value={formData.requirements}
              onChangeText={(text) => setFormData({ ...formData, requirements: text })}
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        <TouchableOpacity style={[styles.submitButton, { backgroundColor: colors.primary }]} onPress={handleSubmit}>
          <Text style={styles.submitButtonText}>Publicar Tarea</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  section: {
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
  },
  categoryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 5,
  },
  categoryButton: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 10,
    marginBottom: 10,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: "500",
  },
  budgetRow: {
    flexDirection: "row",
  },
  urgencyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  urgencyButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: "center",
    marginHorizontal: 5,
  },
  urgencyButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  submitButton: {
    padding: 18,
    borderRadius: 15,
    alignItems: "center",
    marginTop: 10,
  },
  submitButtonText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
  },
})
