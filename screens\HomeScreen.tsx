"use client"

import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Dimensions, SafeAreaView } from "react-native"
import { useTheme } from "../contexts/ThemeContext"
import { useUser } from "../contexts/UserContext"
import { useTasks } from "../contexts/TaskContext"

const { width } = Dimensions.get("window")

export default function HomeScreen({ navigation }: any) {
  const { colors, theme } = useTheme()
  const { user } = useUser()
  const { availableTasks, myTasks } = useTasks()
  const [searchQuery, setSearchQuery] = useState("")

  const categories = [
    { name: "<PERSON><PERSON><PERSON>", icon: "🧹" },
    { name: "Plomería", icon: "🔧" },
    { name: "Electricidad", icon: "⚡" },
    { name: "Jardiner<PERSON>", icon: "🌱" },
    { name: "<PERSON><PERSON><PERSON>", icon: "🎨" },
    { name: "<PERSON><PERSON><PERSON>", icon: "📦" },
    { name: "Carpinter<PERSON>", icon: "🪚" },
    { name: "Tecnología", icon: "💻" },
  ]

  const filteredTasks = availableTasks.filter(
    (task) =>
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.category.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Enhanced Professional Header */}
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <View style={styles.headerTop}>
            <TouchableOpacity
              style={[styles.menuButton, { backgroundColor: colors.background }]}
              onPress={() => navigation.openDrawer()}
            >
              <Text style={[styles.menuIcon, { color: colors.text }]}>☰</Text>
            </TouchableOpacity>

            <View style={styles.headerCenter}>
              <Text style={[styles.greeting, { color: colors.text }]}>TaskApp Professional</Text>
              <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
                {user?.currentMode === "client" ? "Encuentra servicios de calidad" : "Conecta con clientes"}
              </Text>
            </View>

            <TouchableOpacity
              style={[styles.notificationButton, { backgroundColor: colors.background }]}
              onPress={() => navigation.navigate("Notifications")}
            >
              <Text style={[styles.notificationIcon, { color: colors.primary }]}>🔔</Text>
              <View style={[styles.notificationBadge, { backgroundColor: colors.error }]}>
                <Text style={styles.notificationCount}>3</Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Enhanced Search Bar */}
          <View style={[styles.searchContainer, { backgroundColor: colors.background, borderColor: colors.border }]}>
            <View style={styles.searchIcon}>
              <Text style={[styles.searchIconText, { color: colors.textSecondary }]}>🔍</Text>
            </View>
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder={
                user?.currentMode === "client" ? "Buscar servicios profesionales..." : "Buscar oportunidades..."
              }
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            <TouchableOpacity style={[styles.filterButton, { backgroundColor: colors.primary }]}>
              <Text style={styles.filterIcon}>⚙️</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Quick Stats Dashboard */}
        <View style={[styles.statsContainer, { backgroundColor: colors.surface }]}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>
              {user?.currentMode === "client" ? myTasks.length : availableTasks.length}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Mis Proyectos" : "Disponibles"}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.success }]}>4.9</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Calificación</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: colors.accent }]}>
              {user?.currentMode === "client" ? "$2,340" : "$5,680"}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              {user?.currentMode === "client" ? "Invertido" : "Ganado"}
            </Text>
          </View>
        </View>

        {/* Enhanced Quick Actions */}
        {user?.currentMode === "client" && (
          <View style={styles.quickActionsContainer}>
            <TouchableOpacity
              style={[styles.primaryAction, { backgroundColor: colors.primary }]}
              onPress={() => navigation.navigate("CreateTask")}
            >
              <View style={styles.actionIcon}>
                <Text style={styles.actionIconText}>+</Text>
              </View>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Publicar Proyecto</Text>
                <Text style={styles.actionSubtitle}>Describe tu necesidad profesional</Text>
              </View>
            </TouchableOpacity>

            <View style={styles.secondaryActions}>
              <TouchableOpacity
                style={[styles.secondaryAction, { backgroundColor: colors.success }]}
                onPress={() => navigation.navigate("Quotes")}
              >
                <Text style={styles.secondaryActionIcon}>💰</Text>
                <Text style={styles.secondaryActionText}>Cotizaciones</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.secondaryAction, { backgroundColor: colors.accent }]}
                onPress={() => navigation.navigate("Contracts")}
              >
                <Text style={styles.secondaryActionIcon}>📄</Text>
                <Text style={styles.secondaryActionText}>Contratos</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Professional Categories */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Servicios Profesionales</Text>
            <TouchableOpacity onPress={() => navigation.navigate("Tasks")}>
              <Text style={[styles.seeAllText, { color: colors.primary }]}>Ver todos</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.categoriesGrid}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.name}
                style={[styles.categoryCard, { backgroundColor: colors.background, borderColor: colors.border }]}
                onPress={() => navigation.navigate("Tasks", { category: category.name })}
              >
                <View style={[styles.categoryIconContainer, { backgroundColor: colors.primary + "15" }]}>
                  <Text style={styles.categoryIcon}>{category.icon}</Text>
                </View>
                <Text style={[styles.categoryName, { color: colors.text }]}>{category.name}</Text>
                <Text style={[styles.categoryCount, { color: colors.textSecondary }]}>
                  {Math.floor(Math.random() * 50) + 10} disponibles
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Featured Projects */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {user?.currentMode === "client" ? "Proyectos Recomendados" : "Oportunidades Destacadas"}
            </Text>
            <TouchableOpacity onPress={() => navigation.navigate("Tasks")}>
              <Text style={[styles.seeAllText, { color: colors.primary }]}>Ver todos</Text>
            </TouchableOpacity>
          </View>

          {filteredTasks.slice(0, 3).map((task) => (
            <TouchableOpacity
              key={task.id}
              style={[styles.taskCard, { backgroundColor: colors.background, borderColor: colors.border }]}
              onPress={() => navigation.navigate("TaskDetail", { taskId: task.id })}
            >
              <View style={styles.taskHeader}>
                <View style={styles.taskTitleContainer}>
                  <Text style={[styles.taskTitle, { color: colors.text }]}>{task.title}</Text>
                  <View style={[styles.categoryBadge, { backgroundColor: colors.primary + "15" }]}>
                    <Text style={[styles.categoryBadgeText, { color: colors.primary }]}>{task.category}</Text>
                  </View>
                </View>
                <View style={styles.taskMeta}>
                  <Text style={[styles.budgetText, { color: colors.success }]}>
                    ${task.budget.min} - ${task.budget.max}
                  </Text>
                  <View
                    style={[
                      styles.urgencyIndicator,
                      {
                        backgroundColor:
                          task.urgency === "high"
                            ? colors.error
                            : task.urgency === "medium"
                              ? colors.warning
                              : colors.success,
                      },
                    ]}
                  >
                    <Text style={styles.urgencyText}>
                      {task.urgency === "high" ? "Urgente" : task.urgency === "medium" ? "Moderado" : "Flexible"}
                    </Text>
                  </View>
                </View>
              </View>

              <Text style={[styles.taskDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                {task.description}
              </Text>

              <View style={styles.taskFooter}>
                <View style={styles.taskLocation}>
                  <Text style={[styles.locationIcon, { color: colors.textSecondary }]}>📍</Text>
                  <Text style={[styles.locationText, { color: colors.textSecondary }]}>
                    {task.location.address.split(",")[0]}
                  </Text>
                </View>
                <View style={styles.taskTiming}>
                  <Text style={[styles.durationText, { color: colors.textSecondary }]}>
                    {task.estimatedDuration}h • Hace 2h
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Professional Trust Indicators */}
        <View style={[styles.section, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Garantías Profesionales</Text>
          <View style={styles.trustGrid}>
            <View style={styles.trustItem}>
              <View style={[styles.trustIcon, { backgroundColor: colors.success + "15" }]}>
                <Text style={[styles.trustIconText, { color: colors.success }]}>🛡️</Text>
              </View>
              <Text style={[styles.trustText, { color: colors.text }]}>Verificación completa</Text>
              <Text style={[styles.trustSubtext, { color: colors.textSecondary }]}>ID, antecedentes, referencias</Text>
            </View>
            <View style={styles.trustItem}>
              <View style={[styles.trustIcon, { backgroundColor: colors.primary + "15" }]}>
                <Text style={[styles.trustIconText, { color: colors.primary }]}>💳</Text>
              </View>
              <Text style={[styles.trustText, { color: colors.text }]}>Pagos protegidos</Text>
              <Text style={[styles.trustSubtext, { color: colors.textSecondary }]}>Escrow automático</Text>
            </View>
            <View style={styles.trustItem}>
              <View style={[styles.trustIcon, { backgroundColor: colors.warning + "15" }]}>
                <Text style={[styles.trustIconText, { color: colors.warning }]}>📋</Text>
              </View>
              <Text style={[styles.trustText, { color: colors.text }]}>Contratos digitales</Text>
              <Text style={[styles.trustSubtext, { color: colors.textSecondary }]}>Términos claros</Text>
            </View>
            <View style={styles.trustItem}>
              <View style={[styles.trustIcon, { backgroundColor: colors.accent + "15" }]}>
                <Text style={[styles.trustIconText, { color: colors.accent }]}>🏆</Text>
              </View>
              <Text style={[styles.trustText, { color: colors.text }]}>Certificaciones</Text>
              <Text style={[styles.trustSubtext, { color: colors.textSecondary }]}>Profesionales verificados</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    padding: 24,
    paddingTop: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  headerTop: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
  },
  menuButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuIcon: {
    fontSize: 18,
    fontWeight: "600",
  },
  headerCenter: {
    flex: 1,
  },
  greeting: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationIcon: {
    fontSize: 18,
  },
  notificationBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  notificationCount: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "700",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchIconText: {
    fontSize: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: "400",
  },
  filterButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 12,
  },
  filterIcon: {
    fontSize: 14,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginHorizontal: 24,
    marginVertical: 16,
    padding: 20,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: "500",
  },
  quickActionsContainer: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  primaryAction: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255,255,255,0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  actionIconText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "300",
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  actionSubtitle: {
    color: "rgba(255,255,255,0.8)",
    fontSize: 14,
  },
  secondaryActions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  secondaryAction: {
    flex: 1,
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  secondaryActionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  secondaryActionText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  section: {
    marginHorizontal: 24,
    marginBottom: 24,
    padding: 24,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: "600",
  },
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  categoryCard: {
    width: (width - 96) / 4,
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  categoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  categoryIcon: {
    fontSize: 20,
  },
  categoryName: {
    fontSize: 12,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 4,
  },
  categoryCount: {
    fontSize: 10,
    textAlign: "center",
  },
  taskCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  taskTitleContainer: {
    flex: 1,
    marginRight: 16,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
    lineHeight: 22,
  },
  categoryBadge: {
    alignSelf: "flex-start",
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryBadgeText: {
    fontSize: 12,
    fontWeight: "500",
  },
  taskMeta: {
    alignItems: "flex-end",
  },
  budgetText: {
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 8,
  },
  urgencyIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  urgencyText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "600",
  },
  taskDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  taskFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  taskLocation: {
    flexDirection: "row",
    alignItems: "center",
  },
  locationIcon: {
    fontSize: 12,
    marginRight: 6,
  },
  locationText: {
    fontSize: 14,
  },
  taskTiming: {
    alignItems: "flex-end",
  },
  durationText: {
    fontSize: 12,
  },
  trustGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  trustItem: {
    width: "48%",
    alignItems: "center",
    marginBottom: 20,
  },
  trustIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  trustIconText: {
    fontSize: 20,
  },
  trustText: {
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 4,
  },
  trustSubtext: {
    fontSize: 12,
    textAlign: "center",
    lineHeight: 16,
  },
})
