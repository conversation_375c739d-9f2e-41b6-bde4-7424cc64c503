"use client"

import { useState, useRef, useEffect } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from "react-native"
import { useTheme } from "../contexts/ThemeContext"

interface Message {
  id: string
  text: string
  isUser: boolean
  timestamp: Date
}

export default function TuAmigoScreen() {
  const { colors } = useTheme()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      text: "¡Hola! Soy TuAmigo 🤗 Estoy aquí para apoyarte y motivarte. ¿Cómo te sientes hoy?",
      isUser: false,
      timestamp: new Date(),
    },
  ])
  const [inputText, setInputText] = useState("")
  const scrollViewRef = useRef<ScrollView>(null)

  const motivationalResponses = {
    sad: [
      "Entiendo que te sientes triste 😔 Recuerda que los días difíciles también pasan. ¿Quieres contarme qué te preocupa?",
      "Es normal sentirse así a veces. Eres más fuerte de lo que crees 💪 ¿Hay algo específico que te está afectando?",
    ],
    stressed: [
      "El estrés puede ser abrumador 😰 Respira profundo conmigo: inhala... exhala... ¿Te ayuda hablar de lo que te estresa?",
      "Cuando me siento estresado, me gusta recordar que puedo manejar las cosas paso a paso 🧘‍♀️ ¿Qué te parece si organizamos tus tareas?",
    ],
    tired: [
      "Parece que necesitas un descanso 😴 Tu bienestar es importante. ¿Has podido dormir bien últimamente?",
      "El cansancio es la forma en que tu cuerpo te dice que necesita cuidado 💤 ¿Qué tal si planificamos un momento de relajación?",
    ],
    happy: [
      "¡Me alegra saber que estás feliz! 😊 La alegría es contagiosa. ¿Qué te ha hecho sentir tan bien hoy?",
      "¡Qué maravilloso! 🌟 Disfruta este momento de felicidad. ¿Quieres compartir qué te tiene tan contento?",
    ],
    motivated: [
      "¡Esa energía me encanta! 🚀 Cuando estamos motivados podemos lograr grandes cosas. ¿Tienes algún objetivo en mente?",
      "¡Excelente actitud! 💪 La motivación es el primer paso hacia el éxito. ¿En qué quieres enfocar esa energía?",
    ],
    default: [
      "Gracias por compartir conmigo 😊 Estoy aquí para escucharte y apoyarte en lo que necesites.",
      "Me gusta que confíes en mí 🤗 ¿Hay algo más en lo que pueda ayudarte hoy?",
      "Eres una persona valiosa y tus sentimientos importan 💙 ¿Cómo puedo apoyarte mejor?",
      "¡Recuerda que cada día es una nueva oportunidad! 🌅 ¿Qué te gustaría lograr hoy?",
    ],
  }

  const getResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase()

    if (message.includes("triste") || message.includes("sad") || message.includes("deprimido")) {
      return motivationalResponses.sad[Math.floor(Math.random() * motivationalResponses.sad.length)]
    }
    if (
      message.includes("estresado") ||
      message.includes("estrés") ||
      message.includes("ansiedad") ||
      message.includes("preocupado")
    ) {
      return motivationalResponses.stressed[Math.floor(Math.random() * motivationalResponses.stressed.length)]
    }
    if (message.includes("cansado") || message.includes("agotado") || message.includes("tired")) {
      return motivationalResponses.tired[Math.floor(Math.random() * motivationalResponses.tired.length)]
    }
    if (
      message.includes("feliz") ||
      message.includes("contento") ||
      message.includes("alegre") ||
      message.includes("bien")
    ) {
      return motivationalResponses.happy[Math.floor(Math.random() * motivationalResponses.happy.length)]
    }
    if (message.includes("motivado") || message.includes("energía") || message.includes("ánimo")) {
      return motivationalResponses.motivated[Math.floor(Math.random() * motivationalResponses.motivated.length)]
    }

    return motivationalResponses.default[Math.floor(Math.random() * motivationalResponses.default.length)]
  }

  const sendMessage = () => {
    if (!inputText.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputText("")

    // Simulate TuAmigo response
    setTimeout(() => {
      const response: Message = {
        id: (Date.now() + 1).toString(),
        text: getResponse(inputText),
        isUser: false,
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, response])
    }, 1000)
  }

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true })
  }, [messages])

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={styles.headerEmoji}>🤗</Text>
        <View>
          <Text style={[styles.headerTitle, { color: colors.text }]}>TuAmigo</Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>Tu compañero de apoyo emocional</Text>
        </View>
      </View>

      <ScrollView ref={scrollViewRef} style={styles.messagesContainer} contentContainerStyle={styles.messagesContent}>
        {messages.map((message) => (
          <View
            key={message.id}
            style={[styles.messageContainer, message.isUser ? styles.userMessage : styles.friendMessage]}
          >
            <View
              style={[
                styles.messageBubble,
                {
                  backgroundColor: message.isUser ? colors.primary : colors.surface,
                },
              ]}
            >
              <Text style={[styles.messageText, { color: message.isUser ? "#FFFFFF" : colors.text }]}>
                {message.text}
              </Text>
            </View>
            <Text style={[styles.messageTime, { color: colors.textSecondary }]}>
              {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
            </Text>
          </View>
        ))}
      </ScrollView>

      <View style={[styles.inputContainer, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
        <TextInput
          style={[
            styles.textInput,
            { backgroundColor: colors.background, color: colors.text, borderColor: colors.border },
          ]}
          placeholder="Escribe cómo te sientes..."
          placeholderTextColor={colors.textSecondary}
          value={inputText}
          onChangeText={setInputText}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[styles.sendButton, { backgroundColor: colors.primary }]}
          onPress={sendMessage}
          disabled={!inputText.trim()}
        >
          <Text style={styles.sendButtonText}>Enviar</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingTop: 10,
  },
  headerEmoji: {
    fontSize: 40,
    marginRight: 15,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
  },
  headerSubtitle: {
    fontSize: 14,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 20,
  },
  messageContainer: {
    marginBottom: 15,
  },
  userMessage: {
    alignItems: "flex-end",
  },
  friendMessage: {
    alignItems: "flex-start",
  },
  messageBubble: {
    maxWidth: "80%",
    padding: 15,
    borderRadius: 20,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 5,
    marginHorizontal: 5,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    padding: 20,
    borderTopWidth: 1,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 10,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  sendButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
})
